from PIL import Image, ImageDraw, ImageFont
import textwrap
import io

def create_text_card():
    # 创建卡片尺寸和背景颜色
    width, height = 800, 300
    background_color = (245, 245, 250)  # 淡紫色背景
    
    # 创建图像对象
    image = Image.new('RGB', (width, height), background_color)
    draw = ImageDraw.Draw(image)
    
    # 设置文字内容
    text = "在高度极化的政治环境中，中间派选民往往被迫在两个极端之间做出选择，而那些试图维持独立政治立场的个人和组织则面临被边缘化的风险。"
    
    # 尝试加载字体
    try:
        # 尝试不同的中文字体路径
        font_paths = [
            "C:/Windows/Fonts/msyh.ttc",  # Windows 微软雅黑
            "C:/Windows/Fonts/SimHei.ttf",  # Windows 黑体
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]
        
        font = None
        for path in font_paths:
            try:
                font = ImageFont.truetype(path, 24)
                break
            except:
                continue
                
        if font is None:
            font = ImageFont.load_default()
    except:
        font = ImageFont.load_default()
    
    # 绘制渐变背景
    for y in range(height):
        r = int(245 + (250 - 245) * y / height)
        g = int(245 + (248 - 245) * y / height)
        b = int(250 + (255 - 250) * y / height)
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # 绘制外边框
    border_color = (70, 130, 180)  # 钢蓝色
    border_width = 6
    for i in range(border_width):
        draw.rectangle([i, i, width - i - 1, height - i - 1], outline=border_color, width=1)
    
    # 绘制内部装饰边框
    inner_margin = 30
    inner_border_color = (100, 149, 237)  # 矢车菊蓝
    draw.rectangle([inner_margin, inner_margin, width - inner_margin, height - inner_margin], 
                   outline=inner_border_color, width=2)
    
    # 文本自动换行
    max_width = 35  # 每行最大字符数
    lines = []
    
    # 手动处理中文换行
    words = list(text)
    current_line = ""
    for word in words:
        if len(current_line) < max_width:
            current_line += word
        else:
            lines.append(current_line)
            current_line = word
    if current_line:
        lines.append(current_line)
    
    # 计算文本布局
    try:
        # 使用新版PIL的textbbox方法
        sample_bbox = draw.textbbox((0, 0), "测试", font=font)
        line_height = sample_bbox[3] - sample_bbox[1] + 8
    except:
        # 如果textbbox不可用，使用估算值
        line_height = 32
    
    total_text_height = line_height * len(lines)
    start_y = (height - total_text_height) // 2
    
    # 绘制文本阴影效果
    shadow_color = (200, 200, 200)
    shadow_offset = 2
    
    for i, line in enumerate(lines):
        try:
            # 计算每行文本的宽度和位置
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
        except:
            # 如果textbbox不可用，使用估算值
            text_width = len(line) * 15
        
        text_x = (width - text_width) // 2
        text_y = start_y + i * line_height
        
        # 绘制阴影
        draw.text((text_x + shadow_offset, text_y + shadow_offset), 
                  line, font=font, fill=shadow_color)
        
        # 绘制主文本
        text_color = (25, 25, 112)  # 深蓝色
        draw.text((text_x, text_y), line, font=font, fill=text_color)
    
    # 添加装饰元素 - 左上角和右下角的小装饰
    decoration_color = (255, 215, 0)  # 金色
    
    # 左上角装饰
    draw.ellipse([15, 15, 35, 35], fill=decoration_color)
    draw.ellipse([45, 15, 65, 35], fill=decoration_color)
    
    # 右下角装饰
    draw.ellipse([width-65, height-35, width-45, height-15], fill=decoration_color)
    draw.ellipse([width-35, height-35, width-15, height-15], fill=decoration_color)
    
    # 添加引号装饰
    quote_color = (128, 128, 128)
    quote_font_size = 48
    
    try:
        quote_font = ImageFont.truetype(font_paths[0] if font_paths else "arial.ttf", quote_font_size)
    except:
        quote_font = font
    
    # 左引号
    draw.text((50, 50), """, font=quote_font, fill=quote_color)
    
    # 右引号
    draw.text((width-80, height-100), """, font=quote_font, fill=quote_color)
    
    return image

# 创建并显示卡片
card_image = create_text_card()
card_image.show()

# 保存图片
card_image.save("political_quote_card.png", "PNG", quality=95)

print("卡片已创建并保存为 'political_quote_card.png'")

# 如果需要在Jupyter环境中显示
try:
    from IPython.display import Image as IPImage, display
    display(IPImage("political_quote_card.png"))
except ImportError:
    print("在非Jupyter环境中，请查看保存的图片文件")
