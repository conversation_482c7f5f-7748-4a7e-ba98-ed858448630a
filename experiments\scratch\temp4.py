import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 数据准备
elections = ['2019年\n欧洲议会选举', '2019年\n英国大选']
vote_percentages = [31.6, 2.0]
colors = ['#1E90FF', '#FF6B6B']  # 蓝色到红色的渐变，象征从高峰到低谷

# 创建适合移动端的图表尺寸
fig, ax = plt.subplots(figsize=(10, 8))

# 创建条形图
bars = ax.bar(elections, vote_percentages, color=colors, alpha=0.8, width=0.6)

# 添加渐变效果
bars[0].set_edgecolor('darkblue')
bars[0].set_linewidth(2)
bars[1].set_edgecolor('darkred')
bars[1].set_linewidth(2)

# 设置标题和标签
ax.set_title('英国脱欧党的政治过山车\n单一议题政党的兴衰轨迹', 
             fontsize=16, fontweight='bold', pad=20)
ax.set_ylabel('得票率 (%)', fontsize=14, fontweight='bold')

# 设置y轴范围和网格
ax.set_ylim(0, 40)
ax.grid(axis='y', linestyle='--', alpha=0.3)

# 在条形图上添加数值标签
for i, (bar, percentage) in enumerate(zip(bars, vote_percentages)):
    height = bar.get_height()
    if i == 0:  # 第一个柱子（高峰）
        ax.text(bar.get_x() + bar.get_width()/2, height + 1.5, 
                f'{percentage}%\n(英国第一)', 
                ha='center', va='bottom', fontsize=12, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8))
    else:  # 第二个柱子（低谷）
        ax.text(bar.get_x() + bar.get_width()/2, height + 1.5, 
                f'{percentage}%\n(0个议席)', 
                ha='center', va='bottom', fontsize=12, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.8))

# 添加下降箭头
arrow_props = dict(arrowstyle='->', lw=3, color='red')
ax.annotate('', xy=(1, 5), xytext=(0, 28), arrowprops=arrow_props)
ax.text(0.5, 18, '暴跌\n93.7%', ha='center', va='center', 
        fontsize=14, fontweight='bold', color='red',
        bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8))

# 美化图表边框
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_linewidth(1.5)
ax.spines['bottom'].set_linewidth(1.5)

# 添加背景色
ax.set_facecolor('#f8f9fa')

plt.tight_layout()

# 添加说明文字
fig.text(0.02, 0.02, 
         '数据来源：英国选举委员会 | 启示：单一议题政党的脆弱性',
         fontsize=10, style='italic', alpha=0.7)

plt.show()

# 创建第二个图表：时间线展示
fig2, ax2 = plt.subplots(figsize=(12, 6))

# 时间线数据
timeline_dates = ['5月', '12月']
timeline_full = ['2019年5月\n欧洲议会选举', '2019年12月\n英国大选']
months = [5, 12]

# 创建折线图显示急剧下降
ax2.plot(months, vote_percentages, marker='o', linewidth=4, markersize=12, 
         color='#FF4500', markerfacecolor='#FF6347', markeredgecolor='darkred', 
         markeredgewidth=2)

# 填充区域显示下降幅度
ax2.fill_between(months, vote_percentages, alpha=0.3, color='#FF4500')

# 添加数据点标签
for x, y, label in zip(months, vote_percentages, timeline_full):
    offset_y = 3 if y > 15 else -3
    ax2.annotate(f'{label}\n{y}%', xy=(x, y), 
                xytext=(0, offset_y), textcoords='offset points',
                ha='center', fontsize=11, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.4', facecolor='white', 
                         edgecolor='gray', alpha=0.9))

# 设置标题和标签
ax2.set_title('脱欧党2019年政治命运急转直下\n7个月内从巅峰跌落谷底', 
              fontsize=16, fontweight='bold', pad=20)
ax2.set_xlabel('2019年时间线', fontsize=14, fontweight='bold')
ax2.set_ylabel('得票率 (%)', fontsize=14, fontweight='bold')

# 设置x轴
ax2.set_xlim(4, 13)
ax2.set_xticks([5, 6, 7, 8, 9, 10, 11, 12])
ax2.set_xticklabels(['5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'])

# 设置y轴
ax2.set_ylim(0, 40)
ax2.grid(True, linestyle='--', alpha=0.3)

# 添加关键事件标注
ax2.axvline(x=10.31, color='gray', linestyle=':', alpha=0.7)  # 10月31日原定脱欧日期
ax2.text(10.31, 35, '原定脱欧日期\n(10月31日)', ha='center', fontsize=10, 
         bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8))

# 美化图表
ax2.spines['top'].set_visible(False)
ax2.spines['right'].set_visible(False)
ax2.set_facecolor('#f8f9fa')

plt.tight_layout()

# 添加分析文字
fig2.text(0.02, 0.02, 
          '关键洞察：当核心议题失去紧迫性或被主流政党吸纳时，单一议题政党迅速失去影响力',
          fontsize=10, style='italic', alpha=0.7)

plt.show()

# 创建第三个图表：对比分析
fig3, (ax3, ax4) = plt.subplots(1, 2, figsize=(14, 6))

# 左图：得票率对比
categories = ['欧洲议会选举', '英国大选']
values = [31.6, 2.0]
colors_pie = ['#1E90FF', '#FF6B6B']

wedges, texts, autotexts = ax3.pie([31.6, 68.4], labels=['脱欧党', '其他政党'], 
                                   colors=['#1E90FF', '#lightgray'], autopct='%1.1f%%',
                                   startangle=90, textprops={'fontsize': 11})
ax3.set_title('2019年5月\n欧洲议会选举', fontsize=14, fontweight='bold')

# 右图：大选结果
wedges2, texts2, autotexts2 = ax4.pie([2.0, 98.0], labels=['脱欧党', '其他政党'], 
                                       colors=['#FF6B6B', '#lightgray'], autopct='%1.1f%%',
                                       startangle=90, textprops={'fontsize': 11})
ax4.set_title('2019年12月\n英国大选', fontsize=14, fontweight='bold')

# 总标题
fig3.suptitle('脱欧党政治影响力的戏剧性变化', fontsize=16, fontweight='bold', y=0.95)

plt.tight_layout()
plt.show()

print("图表创建完成！")
print(f"关键数据：")
print(f"- 2019年5月欧洲议会选举：{vote_percentages[0]}% (英国第一)")
print(f"- 2019年12月英国大选：{vote_percentages[1]}% (0个议席)")
print(f"- 7个月内下降幅度：{vote_percentages[0] - vote_percentages[1]:.1f}个百分点")
print(f"- 相对下降：{((vote_percentages[0] - vote_percentages[1]) / vote_percentages[0] * 100):.1f}%")
