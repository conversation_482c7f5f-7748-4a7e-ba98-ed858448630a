from PIL import Image, ImageDraw, ImageFont
import textwrap

def create_knowledge_card():
    # 卡片尺寸 - 使用黄金比例
    width, height = 800, 500
    
    # 创建图像
    image = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(image)
    
    # 主题文本
    text = "在高度极化的政治环境中，中间派选民往往被迫在两个极端之间做出选择，而那些试图维持独立政治立场的个人和组织则面临被边缘化的风险。"
    
    # 加载字体
    try:
        font_paths = [
            "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
            "C:/Windows/Fonts/SimHei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
        ]
        
        main_font = None
        title_font = None
        
        for path in font_paths:
            try:
                main_font = ImageFont.truetype(path, 24)
                title_font = ImageFont.truetype(path, 32)
                break
            except:
                continue
                
        if main_font is None:
            main_font = ImageFont.load_default()
            title_font = ImageFont.load_default()
            
    except:
        main_font = ImageFont.load_default()
        title_font = ImageFont.load_default()
    
    # 创建渐变背景
    for y in range(height):
        progress = y / height
        # 从淡蓝到白色的渐变
        r = int(240 + progress * 15)  # 240 -> 255
        g = int(248 + progress * 7)   # 248 -> 255
        b = int(255)                  # 保持255
        draw.line([(0, y), (width, y)], fill=(r, g, b))
    
    # 绘制装饰性边框
    # 外边框
    border_color = (52, 73, 94)  # 深蓝灰
    draw.rectangle([0, 0, width-1, height-1], outline=border_color, width=4)
    
    # 内边框
    inner_margin = 20
    inner_color = (149, 165, 166)  # 淡灰
    draw.rectangle([inner_margin, inner_margin, width-inner_margin, height-inner_margin], 
                   outline=inner_color, width=2)
    
    # 顶部装饰条
    header_height = 80
    header_color = (52, 73, 94)
    draw.rectangle([0, 0, width, header_height], fill=header_color)
    
    # 标题
    title = "政治极化与中间派困境"
    title_color = (255, 255, 255)
    
    # 计算标题位置
    try:
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
    except:
        title_width = len(title) * 20
    
    title_x = (width - title_width) // 2
    title_y = (header_height - 32) // 2
    
    # 绘制标题阴影
    draw.text((title_x + 2, title_y + 2), title, font=title_font, fill=(30, 50, 70))
    # 绘制标题
    draw.text((title_x, title_y), title, font=title_font, fill=title_color)
    
    # 处理正文文本换行
    max_chars_per_line = 28
    lines = []
    current_line = ""
    
    for char in text:
        if len(current_line) < max_chars_per_line:
            current_line += char
        else:
            # 在标点符号处优先换行
            if char in '，。！？；：':
                current_line += char
                lines.append(current_line)
                current_line = ""
            else:
                lines.append(current_line)
                current_line = char
    
    if current_line:
        lines.append(current_line)
    
    # 计算文本区域
    content_start_y = header_height + 40
    line_height = 35
    total_text_height = line_height * len(lines)
    start_y = content_start_y + (height - content_start_y - total_text_height) // 2
    
    # 绘制正文
    text_color = (44, 62, 80)  # 深蓝灰
    shadow_color = (180, 180, 180)
    
    for i, line in enumerate(lines):
        # 计算每行居中位置
        try:
            bbox = draw.textbbox((0, 0), line, font=main_font)
            text_width = bbox[2] - bbox[0]
        except:
            text_width = len(line) * 15
        
        text_x = (width - text_width) // 2
        text_y = start_y + i * line_height
        
        # 绘制文本阴影
        draw.text((text_x + 1, text_y + 1), line, font=main_font, fill=shadow_color)
        # 绘制主文本
        draw.text((text_x, text_y), line, font=main_font, fill=text_color)
    
    # 添加装饰元素
    # 左侧装饰
    accent_color = (52, 152, 219)  # 蓝色
    gold_color = (241, 196, 15)    # 金色
    
    # 左侧圆形装饰
    draw.ellipse([30, header_height + 20, 70, header_height + 60], fill=accent_color)
    draw.ellipse([35, header_height + 25, 65, header_height + 55], outline=gold_color, width=2)
    
    # 右侧装饰
    right_x = width - 70
    draw.ellipse([right_x, header_height + 20, right_x + 40, header_height + 60], fill=accent_color)
    draw.ellipse([right_x + 5, header_height + 25, right_x + 35, header_height + 55], outline=gold_color, width=2)
    
    # 底部装饰线
    bottom_y = height - 30
    draw.line([(50, bottom_y), (width - 50, bottom_y)], fill=gold_color, width=3)
    
    # 装饰点
    for x in range(100, width - 100, 80):
        draw.ellipse([x-3, bottom_y-3, x+3, bottom_y+3], fill=accent_color)
    
    # 添加引号装饰
    quote_color = (149, 165, 166)
    quote_size = 48
    
    try:
        quote_font = ImageFont.truetype(font_paths[0], quote_size)
    except:
        quote_font = main_font
    
    # 左引号
    draw.text((60, content_start_y - 10), """, font=quote_font, fill=quote_color)
    # 右引号  
    draw.text((width - 90, height - 80), """, font=quote_font, fill=quote_color)
    
    return image

# 创建并保存卡片
if __name__ == "__main__":
    try:
        card = create_knowledge_card()
        
        # 保存图片
        filename = "political_knowledge_card.png"
        card.save(filename, "PNG", quality=95)
        print(f"知识卡片已创建并保存为: {filename}")
        
        # 显示图片
        card.show()
        
    except Exception as e:
        print(f"创建卡片时出错: {e}")
        print("请确保已安装 Pillow 库: pip install Pillow")
