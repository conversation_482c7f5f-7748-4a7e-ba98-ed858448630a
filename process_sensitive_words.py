import csv
import json
import os

def extract_keywords_from_csv(file_path):
    keywords = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header row
        for row in reader:
            if row:
                keyword = row[0].strip()
                if keyword:
                    keywords.add(keyword)
    return keywords

def extract_keywords_from_txt(file_path):
    keywords = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            keyword = line.strip()
            if keyword:
                keywords.add(keyword)
    return keywords

def main():
    all_keywords = set()

    # Process chinese-keywords/csv/all.csv
    csv_file_path = "D:\\Research\\Censorship\\Score\\chinese-keywords\\csv\\all.csv"
    if os.path.exists(csv_file_path):
        all_keywords.update(extract_keywords_from_csv(csv_file_path))
        print(f"Extracted {len(all_keywords)} keywords from {csv_file_path}")
    else:
        print(f"File not found: {csv_file_path}")

    # Process tencent-sensitive-words/sensitive_words.txt
    txt_file_path_1 = "D:\\Research\\Censorship\\Score\\tencent-sensitive-words\\sensitive_words.txt"
    if os.path.exists(txt_file_path_1):
        all_keywords.update(extract_keywords_from_txt(txt_file_path_1))
        print(f"Extracted {len(all_keywords)} keywords from {txt_file_path_1}")
    else:
        print(f"File not found: {txt_file_path_1}")

    # Process tencent-sensitive-words/sensitive_words_lines.txt
    txt_file_path_2 = "D:\\Research\\Censorship\\Score\\tencent-sensitive-words\\sensitive_words_lines.txt"
    if os.path.exists(txt_file_path_2):
        all_keywords.update(extract_keywords_from_txt(txt_file_path_2))
        print(f"Extracted {len(all_keywords)} keywords from {txt_file_path_2}")
    else:
        print(f"File not found: {txt_file_path_2}")

    # Convert set to list and sort for consistent output
    sorted_keywords = sorted(list(all_keywords))

    # Save to JSON
    output_json_path = "D:\\Research\\Censorship\\Score\\sensitive_words.json"
    with open(output_json_path, 'w', encoding='utf-8') as f:
        json.dump(sorted_keywords, f, ensure_ascii=False, indent=4)
    print(f"Saved {len(sorted_keywords)} unique keywords to {output_json_path}")

if __name__ == "__main__":
    main()
