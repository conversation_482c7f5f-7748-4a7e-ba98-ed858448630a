import requests
from bs4 import BeautifulSoup
import json
import time

def get_entry_details(entry_url):
    """
    Fetches and parses the details from a single sensitive word entry page.
    """
    details = {
        '敏感词/话题': '',
        '敏感词': '',
        '组合类型': '',
        '敏感度': '',
        '发现时间': '',
        '平台': '',
        '消息来源': '',
        '相关报道': '',
        '其他': '',
        'URL': entry_url
    }
    try:
        response = requests.get(entry_url, timeout=30)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')

        # The title is usually the main topic
        title = soup.find('h1', class_='entry-title')
        if title:
            details['敏感词/话题'] = title.text.strip()

        # The content contains the details
        content = soup.find('div', class_='entry-content')
        if not content:
            return details

        paragraphs = content.find_all('p')
        for p in paragraphs:
            text = p.get_text(separator='|', strip=True)
            parts = [part.strip() for part in text.split('|') if part.strip()]
            
            for part in parts:
                if '敏感词：' in part:
                    details['敏感词'] = part.replace('敏感词：', '').strip()
                elif '组合类型：' in part:
                    details['组合类型'] = part.replace('组合类型：', '').strip()
                elif '敏感度：' in part:
                    details['敏感度'] = part.replace('敏感度：', '').strip()
                elif '发现时间：' in part:
                    details['发现时间'] = part.replace('发现时间：', '').strip()
                elif '平台：' in part:
                    details['平台'] = part.replace('平台：', '').strip()
                elif '消息来源：' in part:
                    details['消息来源'] = part.replace('消息来源：', '').strip()
                elif '相关报道：' in part:
                    details['相关报道'] = part.replace('相关报道：', '').strip()
                elif '其他：' in part:
                    details['其他'] = part.replace('其他：', '').strip()

        return details
    except requests.RequestException as e:
        print(f"Error fetching {entry_url}: {e}")
        return None

def scrape_cdt_sensitive_words():
    """
    Main function to scrape all sensitive words from China Digital Times.
    """
    base_url = "https://chinadigitaltimes.net/chinese/sensitive-words-project"
    all_entries = []
    page_num = 1

    while True:
        if page_num == 1:
            current_url = base_url
        else:
            current_url = f"{base_url}/page/{page_num}"

        print(f"Scraping page: {current_url}")

        try:
            response = requests.get(current_url, timeout=30)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            # Find all entry links on the current page
            entry_links = [a['href'] for a in soup.select('h2.entry-title a')]

            if not entry_links:
                print("No more entries found. Finishing.")
                break

            for link in entry_links:
                details = get_entry_details(link)
                if details:
                    all_entries.append(details)
                    print(f"  - Scraped: {details.get('敏感词/话题', 'N/A')}")
                time.sleep(0.5) # Be respectful to the server

            # Check for a "next page" link to continue
            next_page_link = soup.find('a', class_='next page-numbers')
            if not next_page_link:
                print("Last page reached. Finishing.")
                break
            
            page_num += 1

        except requests.RequestException as e:
            print(f"Error fetching page {page_num}: {e}")
            break
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            break

    # Save the results to a JSON file
    output_filename = 'cdt_sensitive_words.json'
    with open(output_filename, 'w', encoding='utf-8') as f:
        json.dump(all_entries, f, ensure_ascii=False, indent=4)

    print(f"\nScraping complete. {len(all_entries)} entries saved to {output_filename}")

if __name__ == "__main__":
    scrape_cdt_sensitive_words()
