# General Project Structure - Task 2.1

Create data directory structure and organization system.

## Usage
```
/general-project-structure-task-2-1
```

## Description
Set up the data management directory structure with proper organization for raw, processed, and external data.

## Implementation
Create data directory structure:
- Create data/raw/, data/processed/, data/external/ directories
- Add data/raw/keywords/ for sensitive word files
- Create data organization utilities
- _Leverage: existing sensitive_words.json, chinese-keywords/ structure_
- _Requirements: 4.1_

## Spec Reference
From: `.claude/specs/general-project-structure/tasks.md`
Task: 2.1 Create data directory structure