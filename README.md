# Censorship Research Project

A comprehensive semantic analysis toolkit for studying censorship patterns through textual similarity analysis.

## Project Structure

```
├── src/                    # Source code modules
│   ├── analysis/          # Core semantic analysis
│   ├── data_processing/   # Data cleaning and ETL
│   ├── visualization/     # Plotting and charts
│   ├── scraping/          # Web scraping utilities
│   └── utils/             # Shared utilities
├── data/                  # Data files
│   ├── raw/              # Original datasets
│   ├── processed/        # Cleaned data
│   └── external/         # Third-party data
├── output/               # Generated results
│   ├── analysis/         # Analysis results
│   ├── visualizations/   # Generated plots
│   └── reports/          # Generated reports
├── experiments/          # Experimental code
│   ├── notebooks/        # Jupyter notebooks
│   └── scratch/          # Temporary scripts
├── tests/               # Test files
├── scripts/             # Utility scripts
├── docs/                # Documentation
└── config/              # Configuration files
```

## Getting Started

### Prerequisites

Install required dependencies:
```bash
pip install -r requirements.txt
```

### Usage

#### Semantic Analysis
```python
from src.analysis import load_data, calculate_semantic_distance

# Load data and run analysis
keywords, titles = load_data("data/raw/keywords/sensitive_words.json", "path/to/data.rds")
results = calculate_semantic_distance(keywords, titles)
```

#### Configuration
```python
from src.utils.config import config

# Get configuration values
model_name = config.get('analysis_settings.default_model')
threshold = config.get('analysis_settings.similarity_threshold')
```

## Migration from Legacy Structure

If you have existing scripts using the old structure, they should continue to work with backward compatibility features. For optimal organization, consider updating import paths:

```python
# Old way
import semantic_similarity_gpu

# New way  
from src.analysis import semantic_similarity
```

## Documentation

- See `docs/` directory for detailed documentation
- Each module has README files explaining usage
- Configuration is documented in `config/README.md`

## Development

### Running Tests
```bash
python -m pytest tests/
```

### Code Organization
- Production code goes in `src/`
- Experimental code goes in `experiments/`
- Data files are organized by type in `data/`
- Outputs are organized by type in `output/`