# Experiments Directory

This directory contains experimental code, temporary scripts, and research notebooks.

## Structure

- `scratch/` - Temporary experimental scripts and one-off analyses
  - Contains temp*.py files and experimental code
  - Code here may not follow production standards
  - Used for quick prototypes and testing ideas

- `notebooks/` - Jupyter notebooks for interactive analysis
  - Reserved for future notebook-based analysis
  - Interactive exploration and documentation

## Usage

Code in this directory is considered experimental and may not be maintained or documented to production standards. Use for research and prototyping purposes.