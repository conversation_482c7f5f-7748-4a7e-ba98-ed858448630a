
import json
import re

def process_and_split_sensitive_words():
    """
    Reads the scraped data, extracts and splits sensitive words, and saves them to a new JSON file.
    """
    try:
        with open('cdt_sensitive_words.json', 'r', encoding='utf-8') as f:
            data = json.load(f)

        all_words = set()

        for entry in data:
            # Extract words from both '敏感词/话题' and '敏感词' fields
            topic = entry.get('敏感词/话题', '')
            words_str = entry.get('敏感词', '')

            # Combine them for processing
            combined_str = f"{topic} {words_str}"

            # Split by a comprehensive set of delimiters
            # Delimiters: 、 + / （ ） “ ” ， , ; whitespace
            # Also handles cases like '“黄志明”、“陈芳语+玻璃心”' correctly
            # The regex uses a character set `[]` to define all delimiters
            # It also handles surrounding quotes and other non-word characters
            
            # First, remove content within parentheses as it's often explanatory
            cleaned_str = re.sub(r'\（.*?\）|\(.*?\)', '', combined_str)
            
            # Split based on common delimiters
            # This regex will split on one or more occurrences of the specified delimiters
            potential_words = re.split(r'[\s、+/，；“”`]+', cleaned_str)

            for word in potential_words:
                # Clean up each word: remove unwanted characters and strip whitespace
                # This regex removes anything that is not a Chinese character, letter, or number
                clean_word = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', word).strip()
                if clean_word:
                    all_words.add(clean_word)

        # Convert set to list for JSON serialization
        sorted_words = sorted(list(all_words))

        output_filename = 'sensitive_words_cdt.json'
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(sorted_words, f, ensure_ascii=False, indent=4)

        print(f"Processing complete. {len(sorted_words)} unique sensitive words saved to {output_filename}")

    except FileNotFoundError:
        print("Error: 'cdt_sensitive_words.json' not found. Please run the scraping script first.")
    except json.JSONDecodeError:
        print("Error: Could not decode JSON from 'cdt_sensitive_words.json'. The file might be corrupted.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    process_and_split_sensitive_words()
