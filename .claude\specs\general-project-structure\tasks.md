# Implementation Plan

## Task Overview
Implement a comprehensive project structure reorganization for the censorship research codebase. The approach focuses on gradual migration with backward compatibility to ensure existing workflows continue functioning while establishing better organization patterns.

## Steering Document Compliance
Following Python research project best practices with modular organization. Creating structure that supports both development and research workflows with clear separation of concerns.

## Task Format Guidelines
- Use checkbox format: `- [ ] Task number. Task description`
- Include implementation details as bullet points
- Reference requirements using: `_Requirements: X.Y, Z.A_`
- Reference existing code to leverage using: `_Leverage: path/to/file.py, existing/pattern_`
- Focus only on coding tasks (no deployment, user testing, etc.)

## Tasks

- [ ] 1. Create core directory structure
  - Create src/, data/, output/, experiments/, tests/, scripts/, docs/, config/ directories
  - Add __init__.py files to make Python packages
  - Create subdirectories following design specification
  - _Leverage: existing output/ directory structure, chinese-keywords/ organization pattern_
  - _Requirements: 1.1, 1.2_

- [ ] 2. Set up data organization system
  - Overview of data management approach
  - _Requirements: 4.1, 4.2_

- [ ] 2.1 Create data directory structure
  - Create data/raw/, data/processed/, data/external/ directories
  - Add data/raw/keywords/ for sensitive word files
  - Create data organization utilities
  - _Leverage: existing sensitive_words.json, chinese-keywords/ structure_
  - _Requirements: 4.1_

- [ ] 2.2 Migrate existing data files
  - Move sensitive_words*.json to data/raw/keywords/
  - Organize chinese-keywords/ content into data/external/
  - Update data loading utilities with new paths
  - _Leverage: existing JSON file structures, current data loading patterns_
  - _Requirements: 4.2, 4.3_

- [ ] 3. Reorganize source code by functionality
  - Plan source code organization strategy
  - _Requirements: 1.2, 2.1_

- [ ] 3.1 Create analysis module
  - Create src/analysis/ directory and __init__.py
  - Move semantic_similarity_gpu.py to src/analysis/semantic_similarity.py
  - Update imports and maintain existing API
  - _Leverage: semantic_similarity_gpu.py existing functionality_
  - _Requirements: 2.1_

- [ ] 3.2 Create data processing module
  - Create src/data_processing/ directory and __init__.py
  - Move process_*.py files to src/data_processing/
  - Update imports and maintain compatibility
  - _Leverage: process_sensitive_words.py, process_words.py existing logic_
  - _Requirements: 2.1_

- [ ] 3.3 Create scraping module
  - Create src/scraping/ directory and __init__.py
  - Move scrape_*.py files to src/scraping/
  - Update imports and configuration paths
  - _Leverage: scrape_cdt.py, debug_scraper.py existing functionality_
  - _Requirements: 2.1_

- [ ] 3.4 Create visualization module
  - Create src/visualization/ directory and __init__.py
  - Move beautiful_card.py to src/visualization/
  - Organize visualization utilities from semantic_similarity_gpu.py
  - _Leverage: beautiful_card.py, EXTENDED_VISUALIZATION_README.md patterns_
  - _Requirements: 2.1_

- [ ] 3.5 Create utilities module
  - Create src/utils/ directory and __init__.py
  - Extract common utilities from existing scripts
  - Add path resolution and configuration utilities
  - _Leverage: common patterns from existing scripts_
  - _Requirements: 2.1, 2.2_

- [ ] 4. Organize experimental and temporary code
  - Plan experimental code organization
  - _Requirements: 2.2_

- [ ] 4.1 Create experiments directory structure
  - Create experiments/notebooks/ and experiments/scratch/ directories
  - Move temp*.py files to experiments/scratch/
  - Add README explaining experimental code organization
  - _Leverage: existing temp file patterns_
  - _Requirements: 2.2_

- [ ] 4.2 Set up testing infrastructure
  - Create tests/ directory with proper structure
  - Add test utilities and fixtures
  - Create example tests for core functionality
  - _Leverage: existing analysis workflows for test scenarios_
  - _Requirements: 3.1_

- [ ] 5. Create configuration and documentation system
  - Plan configuration management approach
  - _Requirements: 3.1, 3.2_

- [ ] 5.1 Set up configuration system
  - Create config/ directory with environment-specific configs
  - Add path configuration for flexible file locations
  - Create configuration loading utilities
  - _Leverage: existing file path patterns, requirements.txt structure_
  - _Requirements: 1.3, 4.3_

- [ ] 5.2 Organize documentation
  - Create docs/ directory structure
  - Move EXTENDED_VISUALIZATION_README.md to docs/
  - Create project structure documentation
  - Add usage guides for new organization
  - _Leverage: EXTENDED_VISUALIZATION_README.md documentation patterns_
  - _Requirements: 3.2, 3.3_

- [ ] 6. Create migration and compatibility layer
  - Plan backward compatibility strategy
  - _Requirements: All_

- [ ] 6.1 Create migration utilities
  - Write scripts to automate file organization
  - Add symlinks for backward compatibility during transition
  - Create path resolution utilities for gradual migration
  - _Leverage: existing file structures and paths_
  - _Requirements: All_

- [ ] 6.2 Update imports and dependencies
  - Update all __init__.py files for proper imports
  - Add compatibility imports for existing script usage
  - Update requirements.txt with development dependencies
  - _Leverage: existing requirements.txt, current import patterns_
  - _Requirements: 2.3, All_

- [ ] 6.3 Final integration and validation
  - Test all existing workflows work with new structure
  - Validate data loading and output generation
  - Clean up any migration artifacts
  - _Leverage: existing analysis workflows, output patterns_
  - _Requirements: All_