import requests

def inspect_page_html(url):
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        with open("page_content.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("Successfully saved page content to page_content.html")
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")

if __name__ == "__main__":
    inspect_page_html("https://chinadigitaltimes.net/chinese/sensitive-words-project")