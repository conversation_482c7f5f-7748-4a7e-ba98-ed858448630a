---
name: 提交 Pull requests（中文）
about: 用中文提交 Pull requests，按 “Get started” 按钮开始
---
## 摘要
<!--简单说明-->


## 清单
<!--
在适用的框中以 x 替换空格来勾选。您也可以在创建PR后填写这些内容。如果您不确定其中的任何一个，请随时询问。我们在这里为您提供帮助！
-->

- [ ] 我已经阅读了有关提交 `PULL_REQUEST` 的说明
- [ ] 我已经签署了CLA（如果这个项目有）
- [ ] 更改在本地通过测试
- [ ] 我添加了一些测试来证明我的解决方案是有效或功能正常
- [ ] 我添加了必要的文档（如果适用）
- [ ] 任何相关更改已合并并发布在下游模块中
- [ ] 我确认 **避免** 破坏持续集成构建


## 更改类型
<!--
类型您的代码引入了哪些类型的更改？
在适用的框中以 x 替换空格来勾选
-->
- [ ] 错误修正（解决问题的不间断更改）
- [ ] 新功能（增加功能的不间断更改）
- [ ] 重大更改（不向下兼容，可能引起现有功能无法按预期运行）
- [ ] 代码样式更新（对原有代码格式化，重命名）
- [ ] 重构（优化原有代码，无功能更改，无API更改）
- [ ] 文档更新（一般，如果没有其他选择）
- [ ] 其他（请描述）：


## 进一步的注释说明
<!--
如果这是一个相对较大或复杂的更改，请通过解释为什么选择解决方案以及考虑了哪些替代方案等来开始讨论。
-->



## 最终确认
<!--最后，请确认：-->
- [ ] 提交的PR没有问题，完全符合您的预期


<!--
您现在已经参与贡献了呢！喜欢这个项目吗？ 考虑一下给它点个 star 来支持它吧！你的支持是对我最大的鼓励！
-->