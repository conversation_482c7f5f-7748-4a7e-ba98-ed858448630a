<h2 align="center">tencent-sensitive-words</h2>

其他推荐： <a href="https://ip.cjh0613.com/" target="_blank" rel="noopener noreferrer">查询本机访问不同网站的公网 IP</a> ，可以检测代理分流配置，避免被 AI 工具封号。可以查看用户访问 中国网站，国际知名 AI 网站，被封的国际网站，其他国际网站 所使用的 IP

<p align="center">
語言: <a href="https://github.com/cjh0613/tencent-sensitive-words/blob/master/README.md" target="_blank" rel="noopener noreferrer">文言</a>
  | <a href="https://github.com/cjh0613/tencent-sensitive-words/blob/master/README_en.md" target="_blank" rel="noopener noreferrer">English</a>
</p>
<p align="center">
  <a href="#序">序</a> •
  <a href="#資">資</a> •
  <a href="#致謝">致謝</a>
</p>

[![GitHub stars](https://img.shields.io/github/stars/cjh0613/tencent-sensitive-words.svg?style=social)](https://github.com/cjh0613/tencent-sensitive-words/stargazers)     [![GitHub forks](https://img.shields.io/github/forks/cjh0613/tencent-sensitive-words.svg?style=social)](https://github.com/cjh0613/tencent-sensitive-words/network/members)  `【請點擊頁面頂右之 star 與 fork】`

[![GitHub release](https://img.shields.io/github/release/cjh0613/tencent-sensitive-words.svg?label=%E7%89%88%E6%9C%AC)](https://github.com/cjh0613/tencent-sensitive-words/releases/tag/)   ![GitHub top language](https://img.shields.io/github/languages/top/cjh0613/tencent-sensitive-words.svg)  ![GitHub code size in bytes](https://img.shields.io/github/languages/code-size/cjh0613/tencent-sensitive-words.svg)  ![GitHub repo size](https://img.shields.io/github/repo-size/cjh0613/tencent-sensitive-words.svg) ![GitHub](https://img.shields.io/github/license/cjh0613/tencent-sensitive-words.svg) ![platforms](https://img.shields.io/badge/platform-win32%20%7C%20win64%20%7C%20linux%20%7C%20osx-brightgreen.svg)     [![GitHub issues](https://img.shields.io/github/issues/cjh0613/tencent-sensitive-words.svg)](https://github.com/cjh0613/tencent-sensitive-words/issues)  [![GitHub closed issues](https://img.shields.io/github/issues-closed/cjh0613/tencent-sensitive-words.svg)](https://github.com/cjh0613/tencent-sensitive-words/issues?q=is%3Aissue+is%3Aclosed) ![Libraries.io dependency status for GitHub repo](https://img.shields.io/librariesio/github/cjh0613/tencent-sensitive-words.svg)   ![GitHub commit activity](https://img.shields.io/github/commit-activity/m/cjh0613/tencent-sensitive-words.svg)  ![GitHub contributors](https://img.shields.io/github/contributors/cjh0613/tencent-sensitive-words.svg)

[![Stargazers repo roster for @cjh0613/tencent-sensitive-words](https://reporoster.com/stars/cjh0613/tencent-sensitive-words)](https://github.com/cjh0613/tencent-sensitive-words/stargazers)

## 序
騰訊的離線敏感詞庫。

[轻量超严格敏感词库](https://github.com/cjh0613/strict-sensitive-word)

## [資](https://sponsor.cjh0613.com/)

前往 https://sponsor.cjh0613.com/ 以資作者。

若喜歡此倉庫，請**點贊**，不勝感激！

## 致謝

E8014 從元氣騎士的安裝檔中選取出來並分享。

### 貢獻者
<a href="https://github.com/cjh0613/tencent-sensitive-words/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=cjh0613/tencent-sensitive-words" />
</a>

### 其他

[![Powered by DartNode](https://dartnode.com/branding/DN-Open-Source-sm.png)](https://dartnode.com "Powered by DartNode - Free VPS for Open Source")
