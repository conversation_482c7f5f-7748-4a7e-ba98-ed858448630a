import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 数据准备：历年竞选总支出（单位：亿美元）和马斯克捐赠
years = [2016, 2020, 2024]
total_spending = [85.1, 183.4, 160.0]  # 根据OpenSecrets数据调整
musk_donations = [0.0, 0.0, 2.9]  # 马斯克捐赠（亿美元）

# 计算马斯克捐赠占比
musk_ratio = [d / t * 100 if t > 0 else 0 for d, t in zip(musk_donations, total_spending)]

# 创建适合移动端的图表
fig, ax1 = plt.subplots(figsize=(12, 8))

# 绘制总竞选支出柱状图
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
bars = ax1.bar(years, total_spending, color=colors, alpha=0.8, width=2.5)

# 创建第二个y轴绘制马斯克捐赠占比
ax2 = ax1.twinx()
line = ax2.plot(years, musk_ratio, color='#d62728', marker='o', 
                linewidth=3, markersize=8, label='马斯克捐赠占比')

# 设置标题和标签
ax1.set_title('美国总统选举竞选支出与马斯克捐赠占比\n(2016-2024)', 
              fontsize=16, fontweight='bold', pad=25)
ax1.set_xlabel('选举年份', fontsize=14, fontweight='bold')
ax1.set_ylabel('总竞选支出 (亿美元)', fontsize=14, fontweight='bold', color='#1f77b4')
ax2.set_ylabel('马斯克捐赠占比 (%)', fontsize=14, fontweight='bold', color='#d62728')

# 设置x轴
ax1.set_xticks(years)
ax1.set_xticklabels([f'{year}年' for year in years])

# 添加数据标签
for i, (bar, spending) in enumerate(zip(bars, total_spending)):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2, height + 5, 
             f'{spending:.1f}亿', ha='center', va='bottom', 
             fontsize=12, fontweight='bold')

# 为马斯克占比添加标签
for x, y, donation in zip(years, musk_ratio, musk_donations):
    if donation > 0:
        ax2.text(x, y + 0.05, f'{y:.1f}%\n({donation:.1f}亿)', 
                ha='center', va='bottom', fontsize=11, fontweight='bold', 
                color='#d62728')

# 设置坐标轴样式
ax1.tick_params(axis='y', colors='#1f77b4', labelsize=12)
ax2.tick_params(axis='y', colors='#d62728', labelsize=12)
ax1.tick_params(axis='x', labelsize=12)

# 设置y轴范围
ax1.set_ylim(0, 200)
ax2.set_ylim(0, 2.5)

# 添加网格
ax1.grid(True, linestyle='--', alpha=0.3, axis='y')

# 美化图表
ax1.spines['top'].set_visible(False)
ax2.spines['top'].set_visible(False)
ax1.spines['right'].set_visible(False)
ax2.spines['left'].set_visible(False)

# 添加图例
ax1.legend(['总竞选支出'], loc='upper left', fontsize=12, frameon=False)
ax2.legend(['马斯克捐赠占比'], loc='upper right', fontsize=12, frameon=False)

plt.tight_layout()

# 添加说明文字
fig.text(0.02, 0.02, 
         '数据来源：OpenSecrets.org | 说明：2024年马斯克成为历史上最大个人政治捐赠者',
         fontsize=10, style='italic', alpha=0.7)

plt.show()

# 创建第二个图表：展示资金规模对比
fig2, ax = plt.subplots(figsize=(10, 8))

# 2024年资金分解数据
categories = ['总竞选支出', '马斯克捐赠', '马斯克占比']
values = [160.0, 2.9, 157.1]  # 160亿总支出，2.9亿马斯克捐赠，其余157.1亿
colors = ['#1f77b4', '#ff7f0e', '#lightgray']

# 创建饼图
wedges, texts, autotexts = ax.pie(values, labels=categories, colors=colors, 
                                  autopct='%1.1f%%', startangle=90,
                                  textprops={'fontsize': 12, 'fontweight': 'bold'})

# 特殊处理马斯克的部分
wedges[1].set_edgecolor('black')
wedges[1].set_linewidth(2)

ax.set_title('2024年美国总统选举资金构成\n马斯克捐赠相对规模', 
             fontsize=16, fontweight='bold', pad=20)

# 添加数值标签
ax.text(0, -1.3, f'总支出: {values[0]:.1f}亿美元\n马斯克捐赠: {values[1]:.1f}亿美元 ({values[1]/values[0]*100:.1f}%)', 
        ha='center', fontsize=12, 
        bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.3))

plt.tight_layout()
plt.show()

# 创建第三个图表：历史趋势线图
fig3, ax = plt.subplots(figsize=(12, 6))

# 绘制支出趋势线
ax.plot(years, total_spending, marker='o', linewidth=3, markersize=8, 
        color='#1f77b4', label='总竞选支出')

# 填充区域
ax.fill_between(years, total_spending, alpha=0.3, color='#1f77b4')

# 标记马斯克进入政治的时间点
ax.axvline(x=2024, color='red', linestyle='--', alpha=0.7, linewidth=2)
ax.text(2024.2, 150, '马斯克首次\n大规模政治捐赠', fontsize=11, 
        bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

# 设置标题和标签
ax.set_title('美国总统选举竞选支出增长趋势\n(2016-2024)', 
             fontsize=16, fontweight='bold', pad=20)
ax.set_xlabel('选举年份', fontsize=14, fontweight='bold')
ax.set_ylabel('竞选支出 (亿美元)', fontsize=14, fontweight='bold')

# 添加数据标签
for x, y in zip(years, total_spending):
    ax.text(x, y + 8, f'{y:.1f}亿', ha='center', va='bottom', 
            fontsize=12, fontweight='bold')

# 设置坐标轴
ax.set_xticks(years)
ax.set_xticklabels([f'{year}年' for year in years])
ax.set_ylim(0, 200)

# 添加网格
ax.grid(True, linestyle='--', alpha=0.3)

# 美化图表
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)

plt.tight_layout()

# 添加说明
fig3.text(0.02, 0.02, 
          '关键洞察：即使是世界首富马斯克的2.9亿美元捐赠，也仅占2024年总支出的1.8%',
          fontsize=10, style='italic', alpha=0.7)

plt.show()
