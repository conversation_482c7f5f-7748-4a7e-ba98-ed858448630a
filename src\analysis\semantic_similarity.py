# -*- coding: utf-8 -*-
"""
语义相似度计算脚本 (GPU加速版) - 优化版

功能:
1.  自动检测并使用可用的GPU进行计算，若无GPU则回退至CPU。
2.  加载预训练的语言模型 (Sentence-BERT)。
3.  从指定的JSON文件加载敏感词列表，并进行去重和去除仅有标点符号的优化处理。
4.  从R的.rds数据文件加载待分析的文本数据。
5.  将输入的关键词列表和标题列表高效地转换为高维向量。
6.  计算每个标题与所有关键词之间的余弦相似度。
7.  对每个标题，计算并返回两个核心指标：
    a. 最大相似度 (Max Similarity): 与最相似的单个关键词的得分。
    b. 平均相似度 (Average Similarity): 与整个关键词集合的平均得分。
8.  根据预设的阈值，判断标题是否应被标记。
9.  将详细的分析结果保存为CSV文件。
10. 生成2D和3D的可视化图表，直观展示词向量分布。

如何运行:
1.  在Google Colab中，确保已将硬件加速器设置为GPU。
2.  安装必要的库:
    pip install sentence-transformers scikit-learn numpy torch pandas matplotlib seaborn pyreadr
3.  确保 `sensitive_words.json` 和 `sample_URFP.rds` 文件与脚本位于同一目录下。
4.  直接执行此Python文件。
"""

import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import json
import os
import torch
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import pyreadr
import argparse
import logging # Added for logging
import re
import string

# Import data path utilities for new project structure
try:
    from ..utils.data_paths import get_keywords_file, get_output_path, get_legacy_path
except ImportError:
    # Fallback for backward compatibility
    def get_keywords_file(filename):
        return filename
    def get_output_path(output_type, filename):
        return f"./output/{filename}"
    def get_legacy_path(filename):
        return filename

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Ignore transformers library warnings about unused weights for cleaner output
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Define constants for default values
DEFAULT_MODEL_NAME = 'paraphrase-multilingual-MiniLM-L12-v2'
DEFAULT_THRESHOLD = 0.7
DEFAULT_OUTPUT_CSV = "semantic_analysis_results.csv"
DEFAULT_KEYWORDS_PATH = "D:/Research/Censorship/Score/sensitive_words.json"
DEFAULT_RDS_PATH = "D:/Research/Censorship/Dataset/sample_URFP.rds"
DEFAULT_OUTPUT_DIR = "./output" # Default output directory

def clean_and_deduplicate_keywords(keywords: list) -> list:
    """
    清理敏感词列表：去重和去除仅有标点符号的词条

    Args:
        keywords (list): 原始敏感词列表

    Returns:
        list: 清理后的敏感词列表
    """
    if not keywords:
        return []

    print(f"开始清理敏感词列表，原始词条数量: {len(keywords)}")

    # 转换为字符串并去除首尾空白
    cleaned_keywords = []
    for keyword in keywords:
        if keyword is not None:
            cleaned_keyword = str(keyword).strip()
            if cleaned_keyword:  # 确保不是空字符串
                cleaned_keywords.append(cleaned_keyword)

    print(f"去除空值和空白后词条数量: {len(cleaned_keywords)}")

    # 去重（保持原始顺序）
    seen = set()
    deduplicated_keywords = []
    for keyword in cleaned_keywords:
        if keyword not in seen:
            seen.add(keyword)
            deduplicated_keywords.append(keyword)

    print(f"去重后词条数量: {len(deduplicated_keywords)}")

    # 去除仅有标点符号的词条
    # 定义标点符号模式（包括中英文标点符号）
    punctuation_pattern = re.compile(r'^[^\w\u4e00-\u9fff]+$', re.UNICODE)

    filtered_keywords = []
    punctuation_only_count = 0

    for keyword in deduplicated_keywords:
        # 检查是否仅包含标点符号
        if punctuation_pattern.match(keyword):
            punctuation_only_count += 1
        else:
            filtered_keywords.append(keyword)

    print(f"去除仅有标点符号的词条数量: {punctuation_only_count}")
    print(f"最终清理后词条数量: {len(filtered_keywords)}")

    return filtered_keywords

def load_data(keywords_path: str, rds_path: str):
    """
    Loads sensitive keywords from a JSON file and text data (entry_name) from an R .rds file.

    Args:
        keywords_path (str): Absolute path to the JSON file containing sensitive keywords.
        rds_path (str): Absolute path to the R .rds file containing the text data.

    Returns:
        tuple: A tuple containing:
            - list: List of sensitive keywords.
            - list: List of titles (entry_name) from the RDS file.
            Returns (None, None) if data loading fails.
    """
    print("\n--- Step 1: Loading data ---")
    keywords = None
    titles = None

    try:
        with open(keywords_path, 'r', encoding='utf-8') as f:
            raw_keywords = json.load(f)
        print(f"Successfully loaded {len(raw_keywords)} raw keywords from {keywords_path}.")

        # 清理和去重敏感词列表
        keywords = clean_and_deduplicate_keywords(raw_keywords)
        print(f"Final processed keywords count: {len(keywords)}")

    except FileNotFoundError:
        print(f"Error: Keywords file not found at {keywords_path}.")
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {keywords_path}. Check file format.")
    except Exception as e:
        print(f"An unexpected error occurred while loading keywords from {keywords_path}: {e}")

    try:
        # pyreadr directly handles the .rds file
        result = pyreadr.read_r(rds_path)
        # Assuming the data frame is the first (and possibly only) item in the result
        df = result[None]
        if 'entry_name' in df.columns:
            titles = df['entry_name'].tolist()
            print(f"Successfully loaded {len(titles)} titles from {rds_path}.")
        else:
            print(f"Error: 'entry_name' column not found in the RDS file at {rds_path}.")
            print(f"Available columns: {df.columns.tolist()}")
            titles = None
    except FileNotFoundError:
        print(f"Error: RDS file not found at {rds_path}.")
    except Exception as e:
        print(f"An unexpected error occurred while loading RDS file from {rds_path}: {e}")

    return keywords, titles


def calculate_semantic_distance(keywords: list, titles: list, model_name: str = 'paraphrase-multilingual-MiniLM-L12-v2', threshold: float = 0.75, output_csv: str = "analysis_results.csv", output_dir: str = DEFAULT_OUTPUT_DIR):
    """
    Calculates semantic similarity between titles and sensitive keywords using a pre-trained language model.

    Args:
        keywords (list): A list of sensitive keywords.
        titles (list): A list of titles (text data) to be analyzed.
        model_name (str): The name of the Sentence-BERT model to use.
        threshold (float): The similarity threshold for flagging a title as sensitive.
        output_csv (str): The filename for saving the analysis results.
        output_dir (str): Directory to save output CSV and plots.

    Returns:
        tuple: A tuple containing:
            - dict: A dictionary of analysis results for each title.
            - SentenceTransformer: The loaded Sentence-BERT model instance.
            - list: The original list of keywords.
            - list: The original list of titles.
            Returns ({}, None, keywords, titles) if model loading fails.
    """
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"\n--- Step 2: Detected compute device: {device.upper()} ---")

    print(f"--- Step 3: Loading pre-trained model: {model_name} (to {device.upper()}) ---")
    try:
        model = SentenceTransformer(model_name, device=device)
        print("Model loaded successfully.")
    except Exception as e:
        print(f"Error loading model: {e}")
        return {}, None, keywords, titles

    print("\n--- Step 4: Vectorizing keywords and titles ---")
    keyword_embeddings = model.encode(keywords, convert_to_tensor=True, show_progress_bar=True)
    title_embeddings = model.encode(titles, convert_to_tensor=True, show_progress_bar=True)
    print("Text vectorization complete.")

    print("\n--- Step 5: Calculating cosine similarity matrix ---")
    keyword_embeddings_np = keyword_embeddings.cpu().numpy()
    title_embeddings_np = title_embeddings.cpu().numpy()
    similarity_matrix = cosine_similarity(title_embeddings_np, keyword_embeddings_np)
    print("Similarity matrix calculation complete.")

    print(f"\n--- Step 6: Analyzing results, flagging threshold set to: {threshold} ---")
    results_list = []
    for i, title in enumerate(titles):
        sim_scores = similarity_matrix[i]
        max_sim_score = np.max(sim_scores)
        matched_keyword_index = np.argmax(sim_scores)
        matched_keyword = keywords[matched_keyword_index]
        avg_sim_score = np.mean(sim_scores)
        is_flagged = max_sim_score >= threshold
        results_list.append({
            "标题": title,
            "最大相似度分数": float(f"{max_sim_score:.4f}"),
            "最相似关键词": matched_keyword,
            "平均相似度分数": float(f"{avg_sim_score:.4f}"),
            "是否标记 (基于阈值)": str(is_flagged)
        })

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    full_output_csv_path = os.path.join(output_dir, output_csv)
    print(f"\n--- Step 7: Saving results to CSV file: {full_output_csv_path} ---")
    try:
        df_results = pd.DataFrame(results_list)
        df_results.to_csv(full_output_csv_path, index=False, encoding='utf-8-sig')
        print(f"Results successfully saved to {full_output_csv_path}")
    except Exception as e:
        print(f"Error saving CSV file: {e}")

    results_dict = {item["标题"]: {k: v for k, v in item.items() if k != "标题"} for item in results_list}
    return results_dict, model, keywords, titles


def create_visualization_plot(embeddings_np, labels, texts, plot_type, title, output_dir, save_plots, filename_prefix):
    """
    Helper function to create 2D or 3D visualization plots.

    Args:
        embeddings_np (numpy.ndarray): Embeddings array
        labels (list): Labels for each data point
        texts (list): Original text data
        plot_type (str): '2d' or '3d'
        title (str): Plot title
        output_dir (str): Output directory
        save_plots (bool): Whether to save plots
        filename_prefix (str): Prefix for saved filename
    """
    try:
        if plot_type == '2d':
            tsne = TSNE(n_components=2, random_state=42, init='pca', learning_rate='auto')
            embeddings_reduced = tsne.fit_transform(embeddings_np)

            df_plot = pd.DataFrame({
                'x': embeddings_reduced[:, 0],
                'y': embeddings_reduced[:, 1],
                'label': labels
            })

            plt.figure(figsize=(12, 8))
            sns.scatterplot(data=df_plot, x='x', y='y', hue='label', legend='full', alpha=0.7)
            plt.title(title)
            plt.xlabel('Dimension 1')
            plt.ylabel('Dimension 2')
            plt.grid(True)

            if save_plots:
                plot_path = os.path.join(output_dir, f"{filename_prefix}_2d_tsne.png")
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                print(f"2D visualization saved to {plot_path}")
            else:
                plt.show()
            plt.close()

        elif plot_type == '3d':
            tsne = TSNE(
                n_components=3,
                random_state=42,
                init='pca',
                learning_rate='auto',
                perplexity=min(30, len(embeddings_np)//4),
                n_iter=1000,
                verbose=1
            )
            embeddings_reduced = tsne.fit_transform(embeddings_np)

            df_plot = pd.DataFrame({
                'x': embeddings_reduced[:, 0],
                'y': embeddings_reduced[:, 1],
                'z': embeddings_reduced[:, 2],
                'label': labels
            })

            fig = plt.figure(figsize=(14, 10))
            ax = fig.add_subplot(111, projection='3d')

            # Define colors for different labels
            unique_labels = list(set(labels))
            colors = plt.cm.Set1(np.linspace(0, 1, len(unique_labels)))
            color_map = dict(zip(unique_labels, colors))

            for label in unique_labels:
                subset = df_plot[df_plot['label'] == label]
                ax.scatter(subset['x'], subset['y'], subset['z'],
                          c=[color_map[label]], label=label, s=20, alpha=0.7)

            ax.set_title(title)
            ax.set_xlabel('Dimension 1')
            ax.set_ylabel('Dimension 2')
            ax.set_zlabel('Dimension 3')
            ax.legend()

            if save_plots:
                plot_path = os.path.join(output_dir, f"{filename_prefix}_3d_tsne.png")
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                print(f"3D visualization saved to {plot_path}")
            else:
                plt.show()
            plt.close()

    except Exception as e:
        print(f"{plot_type.upper()} t-SNE visualization failed for {filename_prefix}: {e}")


def visualize_embeddings_extended(model, keywords, titles, save_plots: bool = False, output_dir: str = DEFAULT_OUTPUT_DIR, max_samples: int = None):
    """
    Generates multiple types of 2D and 3D visualizations of word embeddings.

    Creates the following visualizations:
    1. Keywords only
    2. Titles only
    3. Combined (keywords + titles)
    4. Random sample of 5000 keywords and titles (if data is large enough)
    5. Multiple random samples of 5000 each

    Args:
        model (SentenceTransformer): The trained Sentence-BERT model.
        keywords (list): List of sensitive keywords.
        titles (list): List of titles (text data).
        save_plots (bool): If True, plots will be saved as PNG files instead of displayed.
        output_dir (str): Directory to save output CSV and plots.
        max_samples (int or None): Maximum number of samples for large dataset visualizations.
    """
    print("\n\n==================== Extended Visualization ====================")

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    import random
    random.seed(42)  # For reproducibility

    # 1. Keywords only visualization
    if len(keywords) > 0:
        print(f"\n--- Visualization 1: Keywords Only ({len(keywords)} items) ---")
        keyword_embeddings = model.encode(keywords, convert_to_tensor=True, show_progress_bar=True)
        keyword_embeddings_np = keyword_embeddings.cpu().numpy()
        keyword_labels = ['Keyword'] * len(keywords)

        # 2D and 3D for keywords only
        create_visualization_plot(keyword_embeddings_np, keyword_labels, keywords, '2d',
                                '2D Visualization of Keywords Only (t-SNE)', output_dir, save_plots, 'keywords_only')
        create_visualization_plot(keyword_embeddings_np, keyword_labels, keywords, '3d',
                                '3D Visualization of Keywords Only (t-SNE)', output_dir, save_plots, 'keywords_only')

    # 2. Titles only visualization
    if len(titles) > 0:
        print(f"\n--- Visualization 2: Titles Only ({len(titles)} items) ---")

        # Sample titles if too many
        titles_for_viz = titles
        if max_samples and len(titles) > max_samples:
            titles_for_viz = random.sample(titles, max_samples)
            print(f"Sampled {len(titles_for_viz)} titles from {len(titles)} for visualization")

        title_embeddings = model.encode(titles_for_viz, convert_to_tensor=True, show_progress_bar=True)
        title_embeddings_np = title_embeddings.cpu().numpy()
        title_labels = ['Title'] * len(titles_for_viz)

        # 2D and 3D for titles only
        create_visualization_plot(title_embeddings_np, title_labels, titles_for_viz, '2d',
                                '2D Visualization of Titles Only (t-SNE)', output_dir, save_plots, 'titles_only')
        create_visualization_plot(title_embeddings_np, title_labels, titles_for_viz, '3d',
                                '3D Visualization of Titles Only (t-SNE)', output_dir, save_plots, 'titles_only')

    # 3. Combined visualization (original functionality)
    print(f"\n--- Visualization 3: Combined Keywords and Titles ---")
    all_texts = keywords + titles

    # Apply sampling if dataset is too large
    if max_samples is not None and len(all_texts) > max_samples:
        print(f"Dataset has {len(all_texts)} samples, sampling {max_samples} for combined visualization...")

        # Ensure we keep some keywords and some titles
        if len(keywords) >= max_samples:
            sampled_indices = random.sample(range(len(all_texts)), max_samples)
            sampled_texts = [all_texts[i] for i in sampled_indices]
            sampled_keyword_count = sum(1 for i in sampled_indices if i < len(keywords))
            sampled_title_count = len(sampled_texts) - sampled_keyword_count
        else:
            sampled_keyword_count = len(keywords)
            sampled_title_count = min(max_samples - len(keywords), len(titles))
            sampled_texts = keywords + random.sample(titles, sampled_title_count)

        all_texts = sampled_texts
        print(f"Sampled {len(all_texts)} texts for combined visualization ({sampled_keyword_count} keywords, {sampled_title_count} titles).")
    else:
        sampled_keyword_count = len(keywords)
        sampled_title_count = len(titles)

    all_embeddings = model.encode(all_texts, convert_to_tensor=True, show_progress_bar=True)
    all_embeddings_np = all_embeddings.cpu().numpy()
    combined_labels = ['Keyword'] * sampled_keyword_count + ['Title'] * sampled_title_count

    # 2D and 3D for combined
    create_visualization_plot(all_embeddings_np, combined_labels, all_texts, '2d',
                            '2D Visualization of Keywords and Titles Combined (t-SNE)', output_dir, save_plots, 'combined')
    create_visualization_plot(all_embeddings_np, combined_labels, all_texts, '3d',
                            '3D Visualization of Keywords and Titles Combined (t-SNE)', output_dir, save_plots, 'combined')

    # 4. Random sample visualizations (5000 items each)
    sample_size = 5000
    total_available = len(keywords) + len(titles)

    if total_available >= sample_size:
        print(f"\n--- Visualization 4: Random Sample of {sample_size} Items ---")

        # Create a balanced sample if possible
        if len(keywords) >= sample_size // 2 and len(titles) >= sample_size // 2:
            # Balanced sample
            sample_keywords = random.sample(keywords, sample_size // 2)
            sample_titles = random.sample(titles, sample_size // 2)
        else:
            # Proportional sample
            keyword_ratio = len(keywords) / total_available
            keyword_sample_size = int(sample_size * keyword_ratio)
            title_sample_size = sample_size - keyword_sample_size

            sample_keywords = random.sample(keywords, min(keyword_sample_size, len(keywords)))
            sample_titles = random.sample(titles, min(title_sample_size, len(titles)))

        sample_texts = sample_keywords + sample_titles
        sample_embeddings = model.encode(sample_texts, convert_to_tensor=True, show_progress_bar=True)
        sample_embeddings_np = sample_embeddings.cpu().numpy()
        sample_labels = ['Keyword'] * len(sample_keywords) + ['Title'] * len(sample_titles)

        # 2D and 3D for random sample
        create_visualization_plot(sample_embeddings_np, sample_labels, sample_texts, '2d',
                                f'2D Visualization of Random Sample ({len(sample_texts)} items) (t-SNE)',
                                output_dir, save_plots, 'random_sample')
        create_visualization_plot(sample_embeddings_np, sample_labels, sample_texts, '3d',
                                f'3D Visualization of Random Sample ({len(sample_texts)} items) (t-SNE)',
                                output_dir, save_plots, 'random_sample')

        # 5. Multiple random samples
        num_additional_samples = 3  # Create 3 additional random samples
        if total_available >= sample_size * 2:  # Only if we have enough data for multiple samples
            print(f"\n--- Visualization 5: Multiple Random Samples ({num_additional_samples} additional samples) ---")

            for i in range(num_additional_samples):
                print(f"Creating random sample {i+2} of {num_additional_samples+1}...")

                # Create different random samples
                random.seed(42 + i + 1)  # Different seed for each sample

                if len(keywords) >= sample_size // 2 and len(titles) >= sample_size // 2:
                    sample_keywords = random.sample(keywords, sample_size // 2)
                    sample_titles = random.sample(titles, sample_size // 2)
                else:
                    keyword_ratio = len(keywords) / total_available
                    keyword_sample_size = int(sample_size * keyword_ratio)
                    title_sample_size = sample_size - keyword_sample_size

                    sample_keywords = random.sample(keywords, min(keyword_sample_size, len(keywords)))
                    sample_titles = random.sample(titles, min(title_sample_size, len(titles)))

                sample_texts = sample_keywords + sample_titles
                sample_embeddings = model.encode(sample_texts, convert_to_tensor=True, show_progress_bar=True)
                sample_embeddings_np = sample_embeddings.cpu().numpy()
                sample_labels = ['Keyword'] * len(sample_keywords) + ['Title'] * len(sample_titles)

                # 2D and 3D for each additional random sample
                create_visualization_plot(sample_embeddings_np, sample_labels, sample_texts, '2d',
                                        f'2D Visualization of Random Sample {i+2} ({len(sample_texts)} items) (t-SNE)',
                                        output_dir, save_plots, f'random_sample_{i+2}')
                create_visualization_plot(sample_embeddings_np, sample_labels, sample_texts, '3d',
                                        f'3D Visualization of Random Sample {i+2} ({len(sample_texts)} items) (t-SNE)',
                                        output_dir, save_plots, f'random_sample_{i+2}')

    print("\n==================== Extended Visualization Complete ====================")


# Keep the original function for backward compatibility
def visualize_embeddings(model, keywords, titles, save_plots: bool = False, output_dir: str = DEFAULT_OUTPUT_DIR, max_samples: int = None):
    """
    Original visualization function - now calls the extended version.
    Kept for backward compatibility.
    """
    visualize_embeddings_extended(model, keywords, titles, save_plots, output_dir, max_samples)

def main():
    """
    Main function to parse arguments, run the semantic similarity analysis, and visualize results.
    """
    parser = argparse.ArgumentParser(description="Perform semantic similarity analysis on text data.")
    parser.add_argument(
        "--keywords_path",
        type=str,
        default="D:/Research/Censorship/Score/sensitive_words.json",
        help="Absolute path to the JSON file containing sensitive keywords."
    )
    parser.add_argument(
        "--rds_path",
        type=str,
        default="D:/Research/Censorship/Dataset/sample_URFP.rds",
        help="Absolute path to the R .rds file containing the text data (entry_name column)."
    )
    parser.add_argument(
        "--model_name",
        type=str,
        default="paraphrase-multilingual-MiniLM-L12-v2",
        help="Name of the Sentence-BERT model to use for embeddings."
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.7,
        help="Similarity threshold for flagging a title as sensitive (default: 0.7)."
    )
    parser.add_argument(
        "--output_csv",
        type=str,
        default="semantic_analysis_results.csv",
        help="Filename for saving the analysis results CSV (default: semantic_analysis_results.csv)."
    )
    parser.add_argument(
        "--save_plots",
        action="store_true",
        help="If set, plots will be saved as PNG files instead of displayed interactively."
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default=DEFAULT_OUTPUT_DIR,
        help="Directory to save output CSV and plots (default: ./output)."
    )
    parser.add_argument(
        "--max_samples",
        type=int,
        default=None,
        help="Maximum number of samples for visualization to improve performance (default: None for all samples)."
    )
    parser.add_argument(
        "--extended_viz",
        action="store_true",
        help="If set, generates extended visualizations including keywords-only, titles-only, and multiple random samples."
    )

    args = parser.parse_args()

    print("\n--- Starting Semantic Similarity Analysis ---")
    keywords, titles = load_data(args.keywords_path, args.rds_path)

    if keywords and titles:
        analysis_results, model_instance, keywords_list, titles_list = calculate_semantic_distance(
            keywords=keywords,
            titles=titles,
            model_name=args.model_name,
            threshold=args.threshold,
            output_csv=args.output_csv,
            output_dir=args.output_dir # Pass output_dir
        )

        if model_instance:
            if args.extended_viz:
                visualize_embeddings_extended(model_instance, keywords_list, titles_list,
                                            save_plots=args.save_plots, output_dir=args.output_dir,
                                            max_samples=args.max_samples)
            else:
                visualize_embeddings(model_instance, keywords_list, titles_list,
                                    save_plots=args.save_plots, output_dir=args.output_dir,
                                    max_samples=args.max_samples)

            print("\n==================== Flagged Titles (Based on Max Similarity) ====================")
            flagged_count = 0
            for title, data in analysis_results.items():
                if data["是否标记 (基于阈值)"] == 'True':
                    flagged_count += 1
                    print(
                        f"Title: \"{title}\"\n"
                        f"  -> Reason: Max similarity with keyword '{data['最相似关键词']}' is {data['最大相似度分数']}\n"
                    )
            if flagged_count == 0:
                print("No titles were flagged based on the current threshold.")
    else:
        print("Data loading failed, cannot proceed with analysis.")

if __name__ == "__main__":
    main()

# --- Senior Data Scientist Review & Optimization Summary ---
# The script has been refactored for improved:
# 1.  **Code Structure and Organization**: Modularized into functions with clear responsibilities, and integrated `argparse` for flexible command-line execution.
# 2.  **Documentation and Clarity**: Enhanced docstrings and added comments for better understanding of logic and parameters.
# 3.  **Reproducibility**: `requirements.txt` has been created, and parameters are now configurable via command-line arguments.
# 4.  **Error Handling**: Improved error messages for data loading.
# 5.  **Performance**: Leverages GPU acceleration via Sentence-BERT and efficient similarity calculation (already present).
# 6.  **Visualization**: Added option to save plots to files, suitable for automated workflows.

# **Further Potential Improvements (Research & Engineering Focus)**:
# - **Model Selection**: Explore and benchmark other pre-trained multilingual or Chinese-specific embedding models (e.g., from Hugging Face Transformers) that might be more suitable for the specific nuances of Chinese censorship analysis.
# - **Threshold Optimization**: Implement methods to dynamically determine or optimize the sensitivity threshold, possibly using a small labeled dataset if available (e.g., ROC curves, precision-recall analysis).
# - **Advanced Scoring Metrics**: Investigate more sophisticated ways to aggregate similarity scores beyond just max and average, or incorporate other linguistic features.
# - **Configuration Management**: For larger projects, consider externalizing parameters into a configuration file (e.g., YAML) for easier management of experimental settings.
# - **Unit Testing**: Develop comprehensive unit tests for each function to ensure robustness and prevent regressions.
# - **Output Management**: Implement an option to specify an output directory for results and plots.
# - **Interactive Visualizations**: For exploratory data analysis, consider interactive plotting libraries (e.g., Plotly, Bokeh) for richer insights into embedding spaces.
# - **Logging Levels**: Implement more granular logging levels (DEBUG, INFO, WARNING, ERROR) for better control over output verbosity.
