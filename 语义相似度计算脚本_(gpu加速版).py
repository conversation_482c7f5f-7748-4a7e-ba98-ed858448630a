
# -*- coding: utf-8 -*-
"""
语义相似度计算脚本 (GPU加速版) - 优化版

功能:
1.  自动检测并使用可用的GPU进行计算，若无GPU则回退至CPU。
2.  加载预训练的语言模型 (Sentence-BERT)。
3.  从指定的JSON文件加载敏感词列表。
4.  从R的.rds数据文件加载待分析的文本数据。
5.  将输入的关键词列表和标题列表高效地转换为高维向量。
6.  计算每个标题与所有关键词之间的余弦相似度。
7.  对每个标题，计算并返回两个核心指标：
    a. 最大相似度 (Max Similarity): 与最相似的单个关键词的得分。
    b. 平均相似度 (Average Similarity): 与整个关键词集合的平均得分。
8.  根据预设的阈值，判断标题是否应被标记。
9.  将详细的分析结果保存为CSV文件。
10. 生成2D和3D的可视化图表，直观展示词向量分布。

如何运行:
1.  在Google Colab中，确保已将硬件加速器设置为GPU。
2.  安装必要的库:
    pip install sentence-transformers scikit-learn numpy torch pandas matplotlib seaborn pyreadr
3.  确保 `sensitive_words.json` 和 `sample_URFP.rds` 文件与脚本位于同一目录下。
4.  直接执行此Python文件。
"""

import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import json
import os
import torch
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import pyreadr

# 忽略transformers库关于未使用权重的警告，使输出更整洁
os.environ["TOKENIZERS_PARALLELISM"] = "false"

def load_data(keywords_path, rds_path):
    """加载关键词和待分析的文本数据"""
    print("\n--- 步骤 1: 正在加载数据 ---")
    try:
        with open(keywords_path, 'r', encoding='utf-8') as f:
            keywords = json.load(f)
        print(f"成功从 {keywords_path} 加载 {len(keywords)} 个关键词。")
    except Exception as e:
        print(f"加载关键词文件失败: {e}")
        return None, None

    try:
        # 获取快捷方式指向的真实路径
        real_rds_path = os.path.realpath(rds_path)
        if not os.path.exists(real_rds_path):
             print(f"RDS文件不存在: {real_rds_path}")
             # Fallback to the provided path if realpath fails
             real_rds_path = rds_path

        result = pyreadr.read_r(real_rds_path)
        df = result[None]
        titles = df['term_name'].tolist()
        print(f"成功从 {rds_path} 加载 {len(titles)} 个标题。")
        return keywords, titles
    except Exception as e:
        print(f"加载RDS文件失败: {e}")
        return None, None


def calculate_semantic_distance(keywords, titles, model_name='paraphrase-multilingual-MiniLM-L12-v2', threshold=0.75, output_csv="analysis_results.csv"):
    """
    使用词嵌入技术计算标题列表与关键词列表之间的语义距离/相似度。
    """
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"\n--- 步骤 2: 检测到计算设备: {device.upper()} ---")

    print(f"--- 步骤 3: 正在加载预训练模型: {model_name} (将加载到 {device.upper()}) ---")
    try:
        model = SentenceTransformer(model_name, device=device)
        print("模型加载成功。")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return {}, None, keywords, titles

    print("\n--- 步骤 4: 正在对关键词和标题进行向量化处理 ---")
    keyword_embeddings = model.encode(keywords, convert_to_tensor=True, show_progress_bar=True)
    title_embeddings = model.encode(titles, convert_to_tensor=True, show_progress_bar=True)
    print("文本向量化完成。")

    print("\n--- 步骤 5: 正在计算余弦相似度矩阵 ---")
    keyword_embeddings_np = keyword_embeddings.cpu().numpy()
    title_embeddings_np = title_embeddings.cpu().numpy()
    similarity_matrix = cosine_similarity(title_embeddings_np, keyword_embeddings_np)
    print("相似度矩阵计算完成。")

    print(f"\n--- 步骤 6: 分析结果，标记阈值设定为: {threshold} ---")
    results_list = []
    for i, title in enumerate(titles):
        sim_scores = similarity_matrix[i]
        max_sim_score = np.max(sim_scores)
        matched_keyword_index = np.argmax(sim_scores)
        matched_keyword = keywords[matched_keyword_index]
        avg_sim_score = np.mean(sim_scores)
        is_flagged = max_sim_score >= threshold
        results_list.append({
            "标题": title,
            "最大相似度分数": float(f"{max_sim_score:.4f}"),
            "最相似关键词": matched_keyword,
            "平均相似度分数": float(f"{avg_sim_score:.4f}"),
            "是否标记 (基于阈值)": str(is_flagged)
        })

    print(f"\n--- 步骤 7: 正在将结果保存到CSV文件: {output_csv} ---")
    try:
        df_results = pd.DataFrame(results_list)
        df_results.to_csv(output_csv, index=False, encoding='utf-8-sig')
        print(f"结果已成功保存到 {output_csv}")
    except Exception as e:
        print(f"保存CSV文件失败: {e}")

    results_dict = {item["标题"]: {k: v for k, v in item.items() if k != "标题"} for item in results_list}
    return results_dict, model, keywords, titles

def visualize_embeddings(model, keywords, titles):
    """生成并显示2D和3D的词向量可视化图"""
    print("\n\n==================== 可视化 ====================")
    all_texts = keywords + titles
    print("\n--- 步骤 8: 正在为可视化生成嵌入向量 ---")
    all_embeddings = model.encode(all_texts, convert_to_tensor=True, show_progress_bar=True)
    all_embeddings_np = all_embeddings.cpu().numpy()

    # 2D 可视化
    print("\n--- 步骤 9: 正在进行2D降维和绘图 ---")
    try:
        tsne_2d = TSNE(n_components=2, random_state=42, init='pca', learning_rate='auto')
        embeddings_2d = tsne_2d.fit_transform(all_embeddings_np)
        df_plot_2d = pd.DataFrame({
            'x': embeddings_2d[:, 0],
            'y': embeddings_2d[:, 1],
            'label': ['关键词'] * len(keywords) + ['标题'] * len(titles)
        })
        plt.figure(figsize=(12, 8))
        sns.scatterplot(data=df_plot_2d, x='x', y='y', hue='label', legend='full', alpha=0.7)
        plt.title('关键词和标题嵌入向量的2D可视化 (t-SNE)')
        plt.xlabel('维度1')
        plt.ylabel('维度2')
        plt.grid(True)
        plt.show()
        print("2D可视化图生成完成。")
    except Exception as e:
        print(f"2D t-SNE 可视化失败: {e}")

    # 3D 可视化
    print("\n--- 步骤 10: 正在进行3D降维和绘图 ---")
    try:
        tsne_3d = TSNE(n_components=3, random_state=42, init='pca', learning_rate='auto')
        embeddings_3d = tsne_3d.fit_transform(all_embeddings_np)
        df_plot_3d = pd.DataFrame({
            'x': embeddings_3d[:, 0],
            'y': embeddings_3d[:, 1],
            'z': embeddings_3d[:, 2],
            'label': ['关键词'] * len(keywords) + ['标题'] * len(titles)
        })
        fig = plt.figure(figsize=(14, 10))
        ax = fig.add_subplot(111, projection='3d')
        colors = {'关键词': 'blue', '标题': 'red'}
        for label, color in colors.items():
            subset = df_plot_3d[df_plot_3d['label'] == label]
            ax.scatter(subset['x'], subset['y'], subset['z'], c=color, label=label, s=20, alpha=0.7)
        ax.set_title('关键词和标题嵌入向量的3D可视化 (t-SNE)')
        ax.set_xlabel('维度1')
        ax.set_ylabel('维度2')
        ax.set_zlabel('维度3')
        ax.legend()
        plt.show()
        print("3D可视化图生成完成。")
    except Exception as e:
        print(f"3D t-SNE 可视化失败: {e}")


if __name__ == "__main__":
    keywords_path = "D:\\Research\\Censorship\\Score\\sensitive_words.json"
    rds_path = "D:\\Research\\Censorship\\Score\\sample_URFP.rds"

    keywords, titles = load_data(keywords_path, rds_path)

    if keywords and titles:
        analysis_results, model_instance, keywords_list, titles_list = calculate_semantic_distance(
            keywords=keywords,
            titles=titles,
            threshold=0.7,
            output_csv="semantic_analysis_results.csv"
        )

        if model_instance:
            visualize_embeddings(model_instance, keywords_list, titles_list)

            print("\n==================== 被标记的标题 (基于最大相似度) ====================")
            flagged_count = 0
            for title, data in analysis_results.items():
                if data["是否标记 (基于阈值)"] == 'True':
                    flagged_count += 1
                    print(
                        f"标题: \"{title}\"\n"
                        f"  -> 命中原因: 与关键词 '{data['最相似关键词']}' 的最大相似度为 {data['最大相似度分数']}\n"
                    )
            if flagged_count == 0:
                print("根据当前阈值，没有标题被标记。")
    else:
        print("由于数据加载失败，无法继续执行。")
