import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 第三政党数据
parties = ['进步党 (1912)', '绿党 (2000)', '自由党 (2016)', '绿党 (2016)']
vote_percentages = [27.4, 2.74, 3.28, 1.1]
colors = ['#2E8B57', '#228B22', '#FFD700', '#32CD32']

# 创建适合移动端的图表尺寸
fig, ax = plt.subplots(figsize=(10, 6))

# 创建水平条形图
bars = ax.barh(parties, vote_percentages, color=colors, alpha=0.8, height=0.6)

# 设置标题和标签
ax.set_xlabel('普选票得票率 (%)', fontsize=12, fontweight='bold')
ax.set_title('美国历史上主要第三政党总统选举表现\n(1912-2016)', 
             fontsize=14, fontweight='bold', pad=20)

# 设置x轴范围和网格
ax.set_xlim(0, 30)
ax.grid(axis='x', linestyle='--', alpha=0.3)

# 在条形图上添加数值标签
for i, (bar, percentage) in enumerate(zip(bars, vote_percentages)):
    width = bar.get_width()
    ax.text(width + 0.5, bar.get_y() + bar.get_height()/2, 
            f'{percentage}%', va='center', ha='left', 
            fontsize=11, fontweight='bold')

# 优化布局
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_linewidth(0.5)
ax.spines['bottom'].set_linewidth(0.5)

# 调整间距
plt.tight_layout()

# 添加说明文字
fig.text(0.02, 0.02, 
         '注：进步党(1912)由前总统罗斯福领导，是美国历史上第三政党的最佳表现',
         fontsize=9, style='italic', alpha=0.7)

plt.show()

# 创建第二个图表：显示第三政党的整体趋势
fig2, ax2 = plt.subplots(figsize=(10, 6))

# 时间线数据
years = [1912, 2000, 2016, 2016]
parties_timeline = ['进步党', '绿党', '自由党', '绿党']
colors_timeline = ['#2E8B57', '#228B22', '#FFD700', '#32CD32']

# 创建散点图显示趋势
scatter = ax2.scatter(years, vote_percentages, 
                     c=colors_timeline, s=200, alpha=0.8, edgecolors='black')

# 为每个点添加标签
for i, (year, party, percentage) in enumerate(zip(years, parties_timeline, vote_percentages)):
    offset_x = -15 if i == 0 else 5
    offset_y = 1 if percentage > 10 else -1
    ax2.annotate(f'{party}\n{percentage}%', 
                xy=(year, percentage), 
                xytext=(offset_x, offset_y), 
                textcoords='offset points',
                fontsize=10, ha='center',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

# 设置标题和标签
ax2.set_xlabel('选举年份', fontsize=12, fontweight='bold')
ax2.set_ylabel('普选票得票率 (%)', fontsize=12, fontweight='bold')
ax2.set_title('美国第三政党总统选举得票率变化趋势\n(1912-2016)', 
              fontsize=14, fontweight='bold', pad=20)

# 设置坐标轴
ax2.set_xlim(1900, 2020)
ax2.set_ylim(0, 30)
ax2.grid(True, linestyle='--', alpha=0.3)

# 添加参考线
ax2.axhline(y=5, color='red', linestyle=':', alpha=0.5, label='5%门槛线')
ax2.axhline(y=15, color='orange', linestyle=':', alpha=0.5, label='15%重要影响线')

# 优化布局
ax2.spines['top'].set_visible(False)
ax2.spines['right'].set_visible(False)
ax2.legend(loc='upper right', fontsize=10)

plt.tight_layout()

# 添加说明文字
fig2.text(0.02, 0.02, 
          '数据来源：美国联邦选举委员会 | 制图说明：1912年进步党表现为历史最佳，现代第三政党影响力有限',
          fontsize=9, style='italic', alpha=0.7)

plt.show()
