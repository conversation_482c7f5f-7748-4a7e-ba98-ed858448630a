# Tests Directory

This directory contains test files for the censorship research project.

## Structure

Tests should be organized to mirror the src/ structure:
- `test_analysis/` - Tests for analysis module
- `test_data_processing/` - Tests for data processing module
- `test_visualization/` - Tests for visualization module
- `test_scraping/` - Tests for scraping module
- `test_utils/` - Tests for utilities

## Usage

Run tests using pytest or your preferred testing framework:
```bash
python -m pytest tests/
```