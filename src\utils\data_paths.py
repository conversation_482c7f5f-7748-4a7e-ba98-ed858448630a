"""
Data path utilities for the censorship research project.

Provides standardized paths for accessing data files in the new organized structure.
Maintains backward compatibility with existing scripts.
"""

import os
from pathlib import Path

# Base paths
PROJECT_ROOT = Path(__file__).parent.parent.parent
DATA_ROOT = PROJECT_ROOT / "data"
OUTPUT_ROOT = PROJECT_ROOT / "output"

# Data paths
RAW_DATA_PATH = DATA_ROOT / "raw"
PROCESSED_DATA_PATH = DATA_ROOT / "processed"
EXTERNAL_DATA_PATH = DATA_ROOT / "external"
KEYWORDS_PATH = RAW_DATA_PATH / "keywords"

# Output paths
ANALYSIS_OUTPUT_PATH = OUTPUT_ROOT / "analysis"
VISUALIZATION_OUTPUT_PATH = OUTPUT_ROOT / "visualizations"
REPORTS_OUTPUT_PATH = OUTPUT_ROOT / "reports"

def get_keywords_file(filename):
    """Get path to a keywords file in the data/raw/keywords directory."""
    return KEYWORDS_PATH / filename

def get_external_data_path(dataset_name):
    """Get path to an external dataset directory."""
    return EXTERNAL_DATA_PATH / dataset_name

def get_output_path(output_type, filename):
    """Get path for output file based on type (analysis, visualizations, reports)."""
    output_dirs = {
        'analysis': ANALYSIS_OUTPUT_PATH,
        'visualizations': VISUALIZATION_OUTPUT_PATH,
        'reports': REPORTS_OUTPUT_PATH
    }
    return output_dirs.get(output_type, OUTPUT_ROOT) / filename

# Backward compatibility - legacy paths
def get_legacy_path(filename):
    """Get legacy path for backward compatibility."""
    legacy_path = PROJECT_ROOT / filename
    if legacy_path.exists():
        return legacy_path
    # Try new organized structure
    if filename.startswith('sensitive_words'):
        return get_keywords_file(filename)
    return legacy_path