#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test script to verify data loading works correctly
"""

import pyreadr
import json

def test_data_loading():
    """Test that we can load both the keywords and RDS data correctly"""
    
    # Test keywords loading
    keywords_path = "D:/Research/Censorship/Score/sensitive_words.json"
    try:
        with open(keywords_path, 'r', encoding='utf-8') as f:
            keywords = json.load(f)
        print(f"✓ Successfully loaded {len(keywords)} keywords")
        print(f"  First few keywords: {keywords[:5]}")
    except Exception as e:
        print(f"✗ Error loading keywords: {e}")
        return False
    
    # Test RDS loading
    rds_path = "D:/Research/Censorship/Dataset/sample_URFP.rds"
    try:
        result = pyreadr.read_r(rds_path)
        df = result[None]
        print(f"✓ Successfully loaded RDS file with shape: {df.shape}")
        print(f"  Available columns: {df.columns.tolist()}")
        
        if 'entry_name' in df.columns:
            titles = df['entry_name'].tolist()
            print(f"✓ Successfully extracted {len(titles)} titles from 'entry_name' column")
            print(f"  First few titles: {titles[:5]}")
            
            # Check for any null values
            null_count = df['entry_name'].isnull().sum()
            print(f"  Null values in entry_name: {null_count}")
            
            return True
        else:
            print(f"✗ 'entry_name' column not found")
            return False
            
    except Exception as e:
        print(f"✗ Error loading RDS file: {e}")
        return False

if __name__ == "__main__":
    print("Testing data loading...")
    success = test_data_loading()
    if success:
        print("\n✓ All data loading tests passed!")
    else:
        print("\n✗ Some tests failed!")
