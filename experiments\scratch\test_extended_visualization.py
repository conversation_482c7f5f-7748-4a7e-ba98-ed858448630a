# -*- coding: utf-8 -*-
"""
测试扩展可视化功能的脚本

这个脚本演示如何使用新的扩展可视化功能来生成多种类型的图表：
1. 只有敏感词列表的可视化
2. 只有词条名列表的可视化  
3. 合并在一起的可视化（原有功能）
4. 随机5000个词条名和敏感词列表的可视化
5. 多个随机样本的可视化

使用方法:
python test_extended_visualization.py
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from semantic_similarity_gpu import load_data, calculate_semantic_distance, visualize_embeddings_extended

def test_extended_visualization():
    """测试扩展可视化功能"""
    
    print("=== 测试扩展可视化功能 ===\n")
    
    # 设置文件路径
    keywords_path = "D:/Research/Censorship/Score/sensitive_words.json"
    rds_path = "D:/Research/Censorship/Dataset/sample_URFP.rds"
    output_dir = "./output_extended_viz"
    
    print("1. 加载数据...")
    keywords, titles = load_data(keywords_path, rds_path)
    
    if not keywords or not titles:
        print("数据加载失败，无法继续测试。")
        return
    
    print(f"   - 加载了 {len(keywords)} 个敏感词")
    print(f"   - 加载了 {len(titles)} 个标题")
    
    print("\n2. 计算语义相似度...")
    analysis_results, model_instance, keywords_list, titles_list = calculate_semantic_distance(
        keywords=keywords,
        titles=titles,
        model_name='paraphrase-multilingual-MiniLM-L12-v2',
        threshold=0.7,
        output_csv="test_analysis_results.csv",
        output_dir=output_dir
    )
    
    if not model_instance:
        print("模型加载失败，无法继续测试。")
        return
    
    print("\n3. 生成扩展可视化图表...")
    print("   这将生成以下类型的图表：")
    print("   - 只有敏感词的2D和3D可视化")
    print("   - 只有标题的2D和3D可视化")
    print("   - 合并的2D和3D可视化")
    print("   - 随机样本的2D和3D可视化")
    print("   - 多个随机样本的2D和3D可视化")
    print("\n   开始生成图表...")
    
    # 使用扩展可视化功能
    visualize_embeddings_extended(
        model=model_instance,
        keywords=keywords_list,
        titles=titles_list,
        save_plots=True,  # 保存图片而不是显示
        output_dir=output_dir,
        max_samples=10000  # 限制最大样本数以提高性能
    )
    
    print(f"\n4. 测试完成！")
    print(f"   所有图表已保存到: {output_dir}")
    print("   生成的文件包括：")
    print("   - keywords_only_2d_tsne.png")
    print("   - keywords_only_3d_tsne.png")
    print("   - titles_only_2d_tsne.png")
    print("   - titles_only_3d_tsne.png")
    print("   - combined_2d_tsne.png")
    print("   - combined_3d_tsne.png")
    print("   - random_sample_2d_tsne.png")
    print("   - random_sample_3d_tsne.png")
    print("   - random_sample_2_2d_tsne.png (如果数据足够)")
    print("   - random_sample_2_3d_tsne.png (如果数据足够)")
    print("   - 等等...")

def test_command_line_usage():
    """演示命令行使用方法"""
    
    print("\n=== 命令行使用方法 ===\n")
    
    print("1. 使用原始可视化功能（只生成合并图表）：")
    print("python semantic_similarity_gpu.py --save_plots")
    
    print("\n2. 使用扩展可视化功能（生成所有类型的图表）：")
    print("python semantic_similarity_gpu.py --save_plots --extended_viz")
    
    print("\n3. 限制样本数量以提高性能：")
    print("python semantic_similarity_gpu.py --save_plots --extended_viz --max_samples 5000")
    
    print("\n4. 自定义输出目录：")
    print("python semantic_similarity_gpu.py --save_plots --extended_viz --output_dir ./my_output")
    
    print("\n5. 完整的命令行示例：")
    print("python semantic_similarity_gpu.py \\")
    print("    --keywords_path \"D:/Research/Censorship/Score/sensitive_words.json\" \\")
    print("    --rds_path \"D:/Research/Censorship/Dataset/sample_URFP.rds\" \\")
    print("    --save_plots \\")
    print("    --extended_viz \\")
    print("    --max_samples 5000 \\")
    print("    --output_dir \"./extended_visualization_output\"")

if __name__ == "__main__":
    # 显示命令行使用方法
    test_command_line_usage()
    
    # 询问用户是否要运行测试
    print("\n" + "="*60)
    response = input("是否要运行扩展可视化测试？这将需要一些时间来处理数据。(y/n): ")
    
    if response.lower() in ['y', 'yes', '是', '1']:
        test_extended_visualization()
    else:
        print("测试已跳过。您可以稍后使用上面显示的命令行方法来运行扩展可视化。")
