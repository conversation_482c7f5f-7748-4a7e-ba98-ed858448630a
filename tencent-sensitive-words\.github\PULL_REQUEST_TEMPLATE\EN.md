---
name: Submit <PERSON>ull requests（English）
about: Submit Pull requests in English, press the "Get started" button to start submitting.
---
## Summary
<!-- Brief description-->


## List
<!--
Replace spaces with x in the boxes to check. 
You can also fill these out after creating the PR. 
If you're unsure about any of them, don't hesitate to ask. We're here to help! 
This is simply a reminder of what we are going to look for before merging your code.
-->

-[ ] I have read the instructions for submitting `PULL_REQUEST`
-[ ] I have signed the CLA (if this project has one)
-[ ] Changes passed the test locally
-[ ] I added some tests to prove that my solution is effective or functional
-[ ] I added the necessary documents (if applicable)
-[ ] Any related changes have been merged and published in downstream modules
-[ ] I confirm **Avoid** breaking the continuous integration build


## Change type
<!--
What types of changes does your code introduce to this repository?
Replace spaces with x in the boxes to check
-->
-[ ] Bug fixes (continuous changes to solve problems)
-[ ] New features (continuous changes to add features)
-[ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
-[ ] Code style update (format and rename the original code)
-[ ] Refactoring (no functional changes, no api changes)
-[ ] Documentation update (generally, if there are no other options)
-[ ] Other (please describe):


## Further notes
<!--
If this is a relatively large or complex change, please start the discussion by explaining why the solution was chosen and what alternatives were considered.
-->



## Final confirmation
<!--Finally, please confirm: -->
-[ ] There is no problem with the PR submitted, and it fully meets your expectations


<!--
You have participated in contributing now! Do you like this r? Consider giving it a star to support it! Your support is my greatest encouragement!
-->