# Design Document

## Overview

This design establishes a comprehensive project structure for the censorship research codebase to organize Python scripts, data files, documentation, and outputs into a logical hierarchy. The structure will support both research workflows and software development best practices while maintaining compatibility with existing analysis tools.

## Steering Document Alignment

### Technical Standards (tech.md)
No tech.md found - will establish standards based on current Python ecosystem patterns using:
- Python best practices for research projects
- Separation of concerns between data, code, and outputs
- Modular design supporting reusable components

### Project Structure (structure.md)
No structure.md found - will create a research-focused structure that supports:
- Clear separation between production and experimental code
- Organized data management with input/output separation
- Documentation co-located with relevant components

## Code Reuse Analysis

### Existing Components to Leverage
- **semantic_similarity_gpu.py**: Core semantic analysis functionality to be organized in analysis module
- **EXTENDED_VISUALIZATION_README.md**: Documentation patterns to be standardized
- **requirements.txt**: Dependency management to be enhanced with dev dependencies
- **chinese-keywords/ directory**: Data organization pattern to be extended

### Integration Points
- **Output directory**: Existing pattern will be expanded with date-based organization
- **Data files**: Current JSON and CSV patterns will be maintained with better organization
- **Analysis scripts**: Current workflow patterns will be preserved while organizing code

## Architecture

The project structure follows a research-oriented architecture with clear separation of concerns:

```mermaid
graph TD
    A[Project Root] --> B[src/ - Source Code]
    A --> C[data/ - Raw Data]
    A --> D[output/ - Results]
    A --> E[docs/ - Documentation]
    A --> F[tests/ - Test Code]
    A --> G[scripts/ - Utilities]
    A --> H[experiments/ - Research]
    
    B --> B1[analysis/ - Core Analysis]
    B --> B2[data_processing/ - ETL]
    B --> B3[visualization/ - Plotting]
    B --> B4[scraping/ - Data Collection]
    
    C --> C1[raw/ - Original Data]
    C --> C2[processed/ - Clean Data]
    C --> C3[external/ - Third Party]
    
    D --> D1[analysis/ - Results]
    D --> D2[visualizations/ - Plots]
    D --> D3[reports/ - Generated Docs]
    
    H --> H1[notebooks/ - Jupyter]
    H --> H2[scratch/ - Temp Work]
```

## Components and Interfaces

### Directory Structure Component
- **Purpose:** Organizes all project files into logical categories
- **Interfaces:** Standard filesystem navigation
- **Dependencies:** None
- **Reuses:** Extends existing output/ and chinese-keywords/ patterns

### Source Code Organization
- **Purpose:** Separates code by functionality (analysis, visualization, scraping, processing)
- **Interfaces:** Python module imports
- **Dependencies:** Existing script functionality
- **Reuses:** Current semantic_similarity_gpu.py, scraping scripts, processing utilities

### Data Management System
- **Purpose:** Organizes raw data, processed data, and external datasets
- **Interfaces:** File path conventions, data loading utilities
- **Dependencies:** Current JSON and CSV data formats
- **Reuses:** Existing sensitive_words.json, CDT data structures

### Output Organization
- **Purpose:** Structures analysis results, visualizations, and reports
- **Interfaces:** Date-based directory naming, file type separation
- **Dependencies:** Current plotting and analysis outputs
- **Reuses:** Existing visualization naming patterns from EXTENDED_VISUALIZATION_README.md

## Data Models

### File Organization Structure
```
project-root/
├── src/
│   ├── analysis/           # Core analysis modules
│   ├── data_processing/    # Data ETL and cleaning
│   ├── visualization/      # Plotting and charts
│   ├── scraping/          # Web scraping utilities
│   └── utils/             # Shared utilities
├── data/
│   ├── raw/               # Original datasets
│   ├── processed/         # Cleaned data
│   └── external/          # Third-party data
├── output/
│   ├── analysis/          # Analysis results
│   ├── visualizations/    # Generated plots
│   └── reports/           # Generated documentation
├── experiments/
│   ├── notebooks/         # Jupyter notebooks
│   └── scratch/           # Temporary experiments
├── tests/                 # Test files
├── scripts/               # Utility scripts
├── docs/                  # Documentation
└── config/                # Configuration files
```

### Migration Mapping
```
Current Files → New Location
semantic_similarity_gpu.py → src/analysis/semantic_similarity.py
scrape_cdt.py → src/scraping/cdt_scraper.py
process_*.py → src/data_processing/
beautiful_card.py → src/visualization/
temp*.py → experiments/scratch/
sensitive_words*.json → data/raw/keywords/
output/ → output/ (reorganized by date)
```

## Error Handling

### Error Scenarios
1. **File Path Migration Issues:**
   - **Handling:** Create symlinks for backward compatibility during transition
   - **User Impact:** Existing scripts continue to work with deprecation warnings

2. **Import Path Changes:**
   - **Handling:** Update __init__.py files to maintain existing imports
   - **User Impact:** Minimal - gradual transition with compatibility layer

3. **Data File Location Changes:**
   - **Handling:** Environment variables and config files for flexible paths
   - **User Impact:** Configuration update required once

## Testing Strategy

### Unit Testing
- Test file organization utilities
- Validate migration scripts work correctly
- Test path resolution functions

### Integration Testing
- Verify existing analysis workflows continue working
- Test data loading from new locations
- Validate output generation in new structure

### End-to-End Testing
- Run complete analysis pipeline with new structure
- Generate visualizations using new organization
- Verify documentation builds correctly