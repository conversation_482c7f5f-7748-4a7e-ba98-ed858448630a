# Configuration Directory

This directory contains configuration files for the censorship research project.

## Files

- `default.json` - Default configuration settings
- `development.json` - Development-specific settings (if needed)
- `production.json` - Production-specific settings (if needed)

## Usage

Configuration is automatically loaded by the `src.utils.config` module:

```python
from src.utils.config import config

# Get configuration values
model_name = config.get('analysis_settings.default_model')
keywords_path = config.get_data_path('keywords_dir')
```

## Settings

### Data Paths
- `keywords_dir` - Directory for sensitive word lists
- `external_data_dir` - Directory for external datasets
- `processed_data_dir` - Directory for processed data

### Output Paths  
- `analysis_dir` - Directory for analysis results
- `visualizations_dir` - Directory for generated plots
- `reports_dir` - Directory for generated reports

### Analysis Settings
- `default_model` - Default sentence transformer model
- `similarity_threshold` - Default similarity threshold
- `max_samples` - Maximum samples for visualization