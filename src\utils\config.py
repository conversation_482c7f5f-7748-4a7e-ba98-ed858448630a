"""
Configuration management for the censorship research project.

Provides centralized configuration loading and management.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any

class Config:
    """Configuration manager for the project."""
    
    def __init__(self, config_file: str = "default.json"):
        self.config_dir = Path(__file__).parent.parent / "config"
        self.config_file = self.config_dir / config_file
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default configuration if file not found
            return self._default_config()
    
    def _default_config(self) -> Dict[str, Any]:
        """Return default configuration."""
        return {
            "data_paths": {
                "keywords_dir": "data/raw/keywords",
                "external_data_dir": "data/external",
                "processed_data_dir": "data/processed"
            },
            "output_paths": {
                "analysis_dir": "output/analysis",
                "visualizations_dir": "output/visualizations", 
                "reports_dir": "output/reports"
            },
            "analysis_settings": {
                "default_model": "paraphrase-multilingual-MiniLM-L12-v2",
                "similarity_threshold": 0.7,
                "max_samples": 5000
            }
        }
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'data_paths.keywords_dir')."""
        keys = key_path.split('.')
        value = self._config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def get_data_path(self, path_type: str) -> Path:
        """Get data path for specified type."""
        base_path = Path(__file__).parent.parent
        relative_path = self.get(f"data_paths.{path_type}")
        return base_path / relative_path if relative_path else base_path
    
    def get_output_path(self, path_type: str) -> Path:
        """Get output path for specified type."""
        base_path = Path(__file__).parent.parent
        relative_path = self.get(f"output_paths.{path_type}")
        return base_path / relative_path if relative_path else base_path

# Global configuration instance
config = Config()