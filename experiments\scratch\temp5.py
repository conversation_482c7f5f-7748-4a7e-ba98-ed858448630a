import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle
import matplotlib.patches as patches

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 数据准备（基于YouGov 2025年6月民调）
# 整体支持率数据
overall_labels = ['不支持任何一方', '支持特朗普', '不确定', '支持马斯克']
overall_values = [52, 28, 11, 8]
overall_colors = ['#d3d3d3', '#ff7f0e', '#ffeb9c', '#1f77b4']

# 党派分解数据
parties = ['共和党选民', '民主党选民', '独立选民']
trump_support = [71, 4, 18]
musk_support = [6, 11, 8]
neither_support = [12, 80, 59]
uncertain = [11, 5, 15]

# 创建综合仪表板
fig = plt.figure(figsize=(14, 10))
gs = fig.add_gridspec(3, 2, height_ratios=[1.2, 1, 1], hspace=0.3, wspace=0.2)

# 主标题
fig.suptitle('马斯克政治前景民调分析\n2025年YouGov调查结果', 
             fontsize=18, fontweight='bold', y=0.95)

# 1. 整体支持率环形图 (左上)
ax1 = fig.add_subplot(gs[0, 0])
wedges, texts, autotexts = ax1.pie(overall_values, labels=overall_labels, 
                                   colors=overall_colors, autopct='%1.1f%%',
                                   startangle=90, counterclock=False,
                                   textprops={'fontsize': 11, 'fontweight': 'bold'},
                                   pctdistance=0.85)

# 创建环形效果
centre_circle = plt.Circle((0,0), 0.60, fc='white')
ax1.add_artist(centre_circle)
ax1.text(0, 0, '总体民调\n(3812人)', ha='center', va='center', 
         fontsize=12, fontweight='bold')
ax1.set_title('马斯克vs特朗普支持率分布', fontsize=14, fontweight='bold', pad=20)

# 2. 关键数据卡片 (右上)
ax2 = fig.add_subplot(gs[0, 1])
ax2.set_xlim(0, 10)
ax2.set_ylim(0, 10)
ax2.axis('off')

# 创建数据卡片
cards_data = [
    ('马斯克支持率', '8%', '#1f77b4', (1, 7, 3.5, 2.5)),
    ('特朗普支持率', '28%', '#ff7f0e', (5.5, 7, 3.5, 2.5)),
    ('政治冷漠度', '52%', '#d3d3d3', (1, 3.5, 3.5, 2.5)),
    ('不确定比例', '11%', '#ffeb9c', (5.5, 3.5, 3.5, 2.5))
]

for title, value, color, (x, y, w, h) in cards_data:
    # 绘制卡片背景
    rect = Rectangle((x, y), w, h, facecolor=color, alpha=0.2, edgecolor=color, linewidth=2)
    ax2.add_patch(rect)
    
    # 添加数值
    ax2.text(x + w/2, y + h/2 + 0.3, value, ha='center', va='center', 
             fontsize=20, fontweight='bold', color=color)
    
    # 添加标题
    ax2.text(x + w/2, y + h/2 - 0.5, title, ha='center', va='center', 
             fontsize=10, fontweight='bold')

ax2.set_title('关键数据概览', fontsize=14, fontweight='bold')

# 3. 党派分解堆叠条形图 (下方)
ax3 = fig.add_subplot(gs[1:, :])

# 创建堆叠条形图的数据
y_pos = np.arange(len(parties))
width = 0.6

# 绘制堆叠条形图
p1 = ax3.barh(y_pos, trump_support, width, label='支持特朗普', color='#ff7f0e', alpha=0.8)
p2 = ax3.barh(y_pos, musk_support, width, left=trump_support, label='支持马斯克', color='#1f77b4', alpha=0.8)
p3 = ax3.barh(y_pos, neither_support, width, 
              left=np.array(trump_support) + np.array(musk_support), 
              label='两人都不支持', color='#d3d3d3', alpha=0.8)
p4 = ax3.barh(y_pos, uncertain, width, 
              left=np.array(trump_support) + np.array(musk_support) + np.array(neither_support), 
              label='不确定', color='#ffeb9c', alpha=0.8)

# 添加数值标签
for i in range(len(parties)):
    # 特朗普支持率标签
    if trump_support[i] > 5:
        ax3.text(trump_support[i]/2, i, f'{trump_support[i]}%', 
                ha='center', va='center', fontweight='bold', fontsize=10)
    
    # 马斯克支持率标签
    if musk_support[i] > 3:
        ax3.text(trump_support[i] + musk_support[i]/2, i, f'{musk_support[i]}%', 
                ha='center', va='center', fontweight='bold', fontsize=10, color='white')
    
    # 两人都不支持标签
    if neither_support[i] > 10:
        ax3.text(trump_support[i] + musk_support[i] + neither_support[i]/2, i, f'{neither_support[i]}%', 
                ha='center', va='center', fontweight='bold', fontsize=10)

# 设置坐标轴
ax3.set_yticks(y_pos)
ax3.set_yticklabels(parties, fontsize=12)
ax3.set_xlabel('支持率百分比', fontsize=12, fontweight='bold')
ax3.set_title('不同党派选民的政治倾向分布', fontsize=14, fontweight='bold', pad=20)
ax3.set_xlim(0, 100)

# 添加图例
ax3.legend(loc='lower right', bbox_to_anchor=(1, 0), ncol=2, fontsize=10)

# 添加网格
ax3.grid(axis='x', linestyle='--', alpha=0.3)

# 美化图表
for spine in ax3.spines.values():
    spine.set_visible(False)

# 添加洞察文本
insight_text = """
关键洞察：
• 超半数选民(52%)对两人都不支持，显示政治疲劳
• 马斯克在所有党派中支持率都偏低
• 共和党选民仍以特朗普为主导(71% vs 6%)
• 民主党选民对马斯克接受度略高于特朗普
"""

fig.text(0.02, 0.02, insight_text, fontsize=10, 
         bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.3))

plt.tight_layout()
plt.show()

# 创建第二个图：马斯克政治困境专题分析
fig2, ((ax4, ax5), (ax6, ax7)) = plt.subplots(2, 2, figsize=(14, 10))

# 1. 马斯克支持率雷达图 (左上)
categories = ['共和党选民', '独立选民', '民主党选民']
musk_ratings = [6, 8, 11]  # 马斯克在各党派的支持率

# 雷达图数据准备
angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False)
musk_ratings_plot = musk_ratings + [musk_ratings[0]]  # 闭合图形
angles_plot = np.concatenate((angles, [angles[0]]))

ax4 = plt.subplot(2, 2, 1, projection='polar')
ax4.plot(angles_plot, musk_ratings_plot, 'o-', linewidth=2, color='#1f77b4')
ax4.fill(angles_plot, musk_ratings_plot, alpha=0.25, color='#1f77b4')
ax4.set_xticks(angles)
ax4.set_xticklabels(categories, fontsize=10)
ax4.set_ylim(0, 15)
ax4.set_title('马斯克跨党派支持率分布', fontsize=12, fontweight='bold', pad=20)
ax4.grid(True)

# 2. 政治冷漠度对比 (右上)
apathy_data = ['总体', '共和党', '民主党', '独立选民']
apathy_rates = [52, 12, 80, 59]
colors_apathy = ['#d3d3d3', '#ffcccb', '#ff6b6b', '#ffa07a']

bars = ax5.bar(apathy_data, apathy_rates, color=colors_apathy, alpha=0.8)
ax5.set_ylabel('不支持任何一方比例 (%)', fontsize=11, fontweight='bold')
ax5.set_title('政治冷漠度党派差异', fontsize=12, fontweight='bold')
ax5.set_ylim(0, 90)

# 添加数值标签
for bar, rate in zip(bars, apathy_rates):
    height = bar.get_height()
    ax5.text(bar.get_x() + bar.get_width()/2, height + 1, f'{rate}%', 
            ha='center', va='bottom', fontweight='bold')

# 3. 马斯克vs特朗普对比 (左下)
comparison_data = ['马斯克总支持率', '特朗普总支持率']
support_rates = [8, 28]
colors_comparison = ['#1f77b4', '#ff7f0e']

bars = ax6.bar(comparison_data, support_rates, color=colors_comparison, alpha=0.8)
ax6.set_ylabel('支持率 (%)', fontsize=11, fontweight='bold')
ax6.set_title('总体支持率直接对比', fontsize=12, fontweight='bold')
ax6.set_ylim(0, 35)

# 添加数值标签和差距标注
for bar, rate in zip(bars, support_rates):
    height = bar.get_height()
    ax6.text(bar.get_x() + bar.get_width()/2, height + 0.5, f'{rate}%', 
            ha='center', va='bottom', fontweight='bold', fontsize=12)

# 添加差距箭头和标注
ax6.annotate('', xy=(1, 28), xytext=(0, 8), 
            arrowprops=dict(arrowstyle='<->', color='red', lw=2))
ax6.text(0.5, 18, '20个百分点差距', ha='center', va='center', 
         fontsize=11, fontweight='bold', color='red',
         bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

# 4. 第三政党支持度调查 (右下)
third_party_labels = ['考虑支持\n马斯克新政党', '不太可能支持', '不确定']
third_party_values = [40, 38, 22]
colors_third = ['#2ca02c', '#ff7f0e', '#ffeb9c']

wedges = ax7.pie(third_party_values, labels=third_party_labels, colors=colors_third, 
                autopct='%1.1f%%', startangle=90, textprops={'fontsize': 10})
ax7.set_title('马斯克"美国党"支持度\n(Quantus Insights调查)', fontsize=12, fontweight='bold')

# 总标题
fig2.suptitle('马斯克政治困境深度分析\n基于2025年最新民调数据', 
              fontsize=16, fontweight='bold', y=0.95)

plt.tight_layout()

# 添加数据来源和结论
fig2.text(0.02, 0.02, 
          '数据来源：YouGov (6月)、Quantus Insights (7月) | 结论：马斯克政治影响力远低于商业声誉',
          fontsize=10, style='italic', alpha=0.7)

plt.show()

print("民调数据可视化完成！")
print("关键发现总结：")
print("1. 马斯克总体支持率仅8%，远低于特朗普的28%")
print("2. 52%的民众对两人都不支持，显示严重的政治疲劳")
print("3. 马斯克在共和党选民中支持率仅6%，政治基础薄弱")
print("4. 尽管如此，40%的人仍考虑支持其新政党，显示变革需求")
