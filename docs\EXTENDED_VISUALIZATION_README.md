# 扩展可视化功能说明

## 概述

我们为 `semantic_similarity_gpu.py` 脚本添加了扩展的可视化功能，现在可以生成多种类型的2D和3D词向量分布图表，帮助您更好地理解数据的分布特征。

## 新增的可视化类型

### 1. 敏感词列表专用可视化
- **文件名**: `keywords_only_2d_tsne.png`, `keywords_only_3d_tsne.png`
- **内容**: 只显示敏感词的词向量分布
- **用途**: 分析敏感词之间的语义关系和聚类情况

### 2. 词条名列表专用可视化
- **文件名**: `titles_only_2d_tsne.png`, `titles_only_3d_tsne.png`
- **内容**: 只显示标题/词条名的词向量分布
- **用途**: 分析标题数据的语义分布特征

### 3. 合并可视化（原有功能）
- **文件名**: `combined_2d_tsne.png`, `combined_3d_tsne.png`
- **内容**: 同时显示敏感词和标题的词向量分布
- **用途**: 对比分析敏感词和标题之间的语义关系

### 4. 随机样本可视化
- **文件名**: `random_sample_2d_tsne.png`, `random_sample_3d_tsne.png`
- **内容**: 随机选择5000个词条（敏感词+标题）的可视化
- **用途**: 在大数据集上进行快速可视化分析

### 5. 多个随机样本可视化
- **文件名**: `random_sample_2_2d_tsne.png`, `random_sample_3_2d_tsne.png`, 等
- **内容**: 生成多个不同的随机样本可视化
- **用途**: 验证可视化结果的稳定性和一致性

## 使用方法

### 命令行使用

#### 基本用法（原有功能）
```bash
python semantic_similarity_gpu.py --save_plots
```

#### 扩展可视化功能
```bash
python semantic_similarity_gpu.py --save_plots --extended_viz
```

#### 限制样本数量（提高性能）
```bash
python semantic_similarity_gpu.py --save_plots --extended_viz --max_samples 5000
```

#### 自定义输出目录
```bash
python semantic_similarity_gpu.py --save_plots --extended_viz --output_dir ./my_visualization_output
```

#### 完整示例
```bash
python semantic_similarity_gpu.py \
    --keywords_path "D:/Research/Censorship/Score/sensitive_words.json" \
    --rds_path "D:/Research/Censorship/Dataset/sample_URFP.rds" \
    --save_plots \
    --extended_viz \
    --max_samples 5000 \
    --output_dir "./extended_visualization_output"
```

### 程序化使用

```python
from semantic_similarity_gpu import load_data, calculate_semantic_distance, visualize_embeddings_extended

# 加载数据
keywords, titles = load_data(keywords_path, rds_path)

# 计算语义相似度
analysis_results, model_instance, keywords_list, titles_list = calculate_semantic_distance(
    keywords=keywords,
    titles=titles,
    model_name='paraphrase-multilingual-MiniLM-L12-v2',
    threshold=0.7,
    output_csv="analysis_results.csv",
    output_dir="./output"
)

# 生成扩展可视化
visualize_embeddings_extended(
    model=model_instance,
    keywords=keywords_list,
    titles=titles_list,
    save_plots=True,
    output_dir="./output",
    max_samples=5000
)
```

## 新增的命令行参数

- `--extended_viz`: 启用扩展可视化功能
- `--max_samples`: 限制可视化的最大样本数量（默认：无限制）
- `--output_dir`: 指定输出目录（默认：`./output`）

## 性能优化建议

1. **大数据集处理**: 当数据集很大时，使用 `--max_samples` 参数限制样本数量
2. **内存管理**: 扩展可视化会生成多个图表，建议确保有足够的内存
3. **GPU加速**: 确保CUDA可用以加速词向量计算
4. **批量处理**: 所有图表会自动保存到指定目录，无需手动干预

## 输出文件说明

所有生成的图表都会保存为高分辨率PNG文件（300 DPI），包含以下信息：
- 清晰的标题和轴标签
- 图例说明不同类型的数据点
- 网格线便于读取坐标
- 透明度设置避免数据点重叠

## 测试脚本

运行 `test_extended_visualization.py` 来测试新功能：

```bash
python test_extended_visualization.py
```

这个脚本会：
1. 演示命令行使用方法
2. 可选择运行完整的可视化测试
3. 生成所有类型的示例图表

## 技术细节

- **降维算法**: 使用t-SNE进行2D和3D降维
- **随机种子**: 设置为42确保结果可重现
- **采样策略**: 平衡采样确保敏感词和标题的合理比例
- **颜色编码**: 不同类型的数据使用不同颜色区分
- **文件命名**: 使用描述性前缀便于识别和组织

## 故障排除

1. **内存不足**: 减少 `max_samples` 参数值
2. **GPU问题**: 脚本会自动回退到CPU模式
3. **文件路径错误**: 确保数据文件路径正确
4. **依赖库问题**: 确保安装了所有必需的Python库

## 后续改进建议

1. 添加交互式可视化（使用Plotly）
2. 支持更多降维算法（PCA, UMAP等）
3. 添加聚类分析功能
4. 支持自定义颜色方案
5. 添加统计信息显示
