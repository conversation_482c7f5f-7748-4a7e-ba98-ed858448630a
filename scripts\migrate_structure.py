#!/usr/bin/env python3
"""
Migration utility for transitioning to the new project structure.

This script helps create symlinks for backward compatibility and 
validates that the new structure works correctly.
"""

import os
import sys
from pathlib import Path

def create_compatibility_symlinks():
    """Create symlinks for backward compatibility with old file locations."""
    
    project_root = Path(__file__).parent.parent
    
    # Map old file locations to new locations
    symlink_mappings = {
        'semantic_similarity_gpu.py': 'src/analysis/semantic_similarity.py',
        'process_sensitive_words.py': 'src/data_processing/process_sensitive_words.py', 
        'process_words.py': 'src/data_processing/process_words.py',
        'scrape_cdt.py': 'src/scraping/cdt_scraper.py',
        'beautiful_card.py': 'src/visualization/beautiful_card.py'
    }
    
    print("Creating compatibility symlinks...")
    
    for old_path, new_path in symlink_mappings.items():
        old_file = project_root / old_path
        new_file = project_root / new_path
        
        # Only create symlink if old file doesn't exist and new file does
        if not old_file.exists() and new_file.exists():
            try:
                old_file.symlink_to(new_file)
                print(f"Created symlink: {old_path} -> {new_path}")
            except OSError as e:
                print(f"Warning: Could not create symlink for {old_path}: {e}")
        elif old_file.exists():
            print(f"Skipped {old_path} (already exists)")
        else:
            print(f"Warning: {new_path} not found")

def validate_structure():
    """Validate that the new project structure is correctly set up."""
    
    project_root = Path(__file__).parent.parent
    
    required_dirs = [
        'src/analysis',
        'src/data_processing', 
        'src/visualization',
        'src/scraping',
        'src/utils',
        'data/raw/keywords',
        'data/processed',
        'data/external',
        'output/analysis',
        'output/visualizations',
        'output/reports',
        'experiments/notebooks',
        'experiments/scratch',
        'tests',
        'docs',
        'config'
    ]
    
    print("\nValidating project structure...")
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if not full_path.exists():
            missing_dirs.append(dir_path)
        else:
            print(f"✓ {dir_path}")
    
    if missing_dirs:
        print(f"\n❌ Missing directories: {missing_dirs}")
        return False
    
    print("\n✅ Project structure validation passed!")
    return True

def test_imports():
    """Test that new module imports work correctly."""
    
    print("\nTesting module imports...")
    
    try:
        # Add src to path for testing
        sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))
        
        # Test imports
        from utils.data_paths import get_keywords_file, get_output_path
        from utils.config import config
        
        print("✓ Data paths utilities imported successfully")
        print("✓ Configuration module imported successfully")
        
        # Test configuration access
        model = config.get('analysis_settings.default_model')
        print(f"✓ Configuration access works: model = {model}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing imports: {e}")
        return False

def main():
    """Run migration and validation."""
    
    print("Project Structure Migration Utility")
    print("=" * 40)
    
    # Validate structure
    structure_ok = validate_structure()
    
    # Create compatibility symlinks
    create_compatibility_symlinks()
    
    # Test imports
    imports_ok = test_imports()
    
    print("\n" + "=" * 40)
    if structure_ok and imports_ok:
        print("✅ Migration completed successfully!")
        print("\nNext steps:")
        print("1. Test existing workflows with the new structure")
        print("2. Update any remaining hardcoded paths in scripts")
        print("3. Consider removing old symlinks once migration is complete")
    else:
        print("❌ Migration completed with warnings")
        print("Please review the issues above before proceeding")

if __name__ == "__main__":
    main()