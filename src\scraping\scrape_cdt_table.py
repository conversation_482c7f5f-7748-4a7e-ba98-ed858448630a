import requests
from bs4 import BeautifulSoup
import json

def scrape_cdt_sensitive_words_from_table():
    """
    Scrapes sensitive words data directly from the table on the main page.
    """
    url = "https://chinadigitaltimes.net/chinese/sensitive-words-project"
    all_entries = []

    try:
        response = requests.get(url, timeout=60)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')

        table = soup.find('table', id='footable_663105')
        if not table:
            print("Could not find the data table.")
            return

        tbody = table.find('tbody')
        if not tbody:
            print("Could not find the table body.")
            return

        rows = tbody.find_all('tr')
        print(f"Found {len(rows)} rows in the table.")

        for row in rows:
            cells = row.find_all('td')
            if len(cells) < 9:
                continue

            entry = {
                '敏感词/话题': cells[0].text.strip(),
                '敏感词': cells[1].text.strip(),
                '组合类型': cells[2].text.strip(),
                '敏感度': cells[3].text.strip(),
                '发现时间': cells[4].text.strip(),
                '平台': cells[5].text.strip(),
                '消息来源': '',
                '相关报道': '',
                '其他': cells[8].text.strip(),
                'URL': '' # No individual entry URL available in this structure
            }

            # Extract link from '消息来源'
            source_link = cells[6].find('a')
            if source_link and source_link.has_attr('href'):
                entry['消息来源'] = source_link['href']

            # Extract link from '相关报道'
            report_link = cells[7].find('a')
            if report_link and report_link.has_attr('href'):
                entry['相关报道'] = report_link['href']

            all_entries.append(entry)

        output_filename = 'cdt_sensitive_words.json'
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(all_entries, f, ensure_ascii=False, indent=4)

        print(f"\nScraping complete. {len(all_entries)} entries saved to {output_filename}")

    except requests.RequestException as e:
        print(f"Error fetching the page: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    scrape_cdt_sensitive_words_from_table()
